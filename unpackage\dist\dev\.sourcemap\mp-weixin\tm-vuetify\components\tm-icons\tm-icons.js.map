{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-icons/tm-icons.vue?d9db", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-icons/tm-icons.vue?e33c", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-icons/tm-icons.vue?1195", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-icons/tm-icons.vue?7b74", "uni-app:///tm-vuetify/components/tm-icons/tm-icons.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-icons/tm-icons.vue?ed90", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-icons/tm-icons.vue?3dc6"], "names": ["props", "dense", "type", "default", "black", "prefx", "name", "size", "color", "iconType", "fllowTheme", "computed", "iconName", "prefx_computed", "black_tmeme", "vtype", "sizes", "color_tmeme", "colors", "get", "set", "data", "colorTheme", "methods", "onclick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4pB,CAAgB,0pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkDhrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,eAUA;EACAA;IACAC;MACA;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MAAA;MACAC;IACA;IACAG;MACAJ;MAAA;MACAC;IACA;IACAI;MACAL;MAAA;MACAC;IACA;IACAK;MACAN;MAAA;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;;IACA;IACAO;MACAR;MACAC;IACA;EACA;EACAQ;IACAC;MAWA;IACA;IACAC;MACA;MACA;MACA;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;QACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACAC;QACA;QACA;UACA;QACA;QACA;MACA;MACAC;QACA;UACA;UACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5KA;AAAA;AAAA;AAAA;AAA2wC,CAAgB,isCAAG,EAAC,C;;;;;;;;;;;ACA/xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-icons/tm-icons.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-icons.vue?vue&type=template&id=7b090d4d&scoped=true&\"\nvar renderjs\nimport script from \"./tm-icons.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-icons.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-icons.vue?vue&type=style&index=0&id=7b090d4d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7b090d4d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-icons/tm-icons.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-icons.vue?vue&type=template&id=7b090d4d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-icons.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-icons.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 图标 -->\r\n\t<view @click=\"onclick\" v-if=\"name\" class=\"tm-icons \" >\r\n\t\t<view\r\n\t\t\tclass=\"tm-icons-item \"\r\n\t\t\t:style=\"{\r\n\t\t\t\twidth: sizes,\r\n\t\t\t\theight: sizes,\r\n\t\t\t\tlineHeight: sizes\r\n\t\t\t}\"\r\n\t\t>\r\n\t\t\t<block v-if=\"vtype == false\">\r\n\t\t\t\t<image\r\n\t\t\t\t\t:src=\"name\"\r\n\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\twidth: sizes,\r\n\t\t\t\t\t\theight: sizes\r\n\t\t\t\t\t}\"\r\n\t\t\t\t\tmode=\"scaleToFill\"\r\n\t\t\t\t></image>\r\n\t\t\t</block>\r\n\t\t\t<block v-if=\"vtype == true\">\r\n\t\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t\t<text\r\n\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\tfontSize: sizes,\r\n\t\t\t\t\t\tfontFamily: 'iconfontTM'\r\n\t\t\t\t\t}\"\r\n\t\t\t\t\tclass=\"icons \"\r\n\t\t\t\t\t:class=\"[black_tmeme ? colors + '-bk' : colors, dense ? '' : 'pa-10', black ? 'opacity-6' : '']\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{ iconName }}\r\n\t\t\t\t</text>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t\t<text\r\n\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\tfontSize: sizes,\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\"\r\n\t\t\t\t\tclass=\"icons \"\r\n\t\t\t\t\t:class=\"[prefx_computed, black_tmeme ? 'bk' : '', iconName, colorTheme ? colors : '', dense ? '' : 'pa-10', black ? 'opacity-6' : '']\"\r\n\t\t\t\t></text>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * 图标组件\r\n * @property {Boolean} dense = [true|false] 默认false,是否去除边距\r\n * @property {String} prefx = [iconfont] 默认iconfont,默认图标的前缀，对自定义图标时有好处。\r\n * @property {String} name = [] 默认'',图标名称，注意不带前缀。\r\n * @property {String | Number} size = [] 默认28, 图标大小，单位是upx\r\n * @property {String} color = [primary] 默认primary, 图标主题颜色名\r\n * @property {Function} click 图标点击事件。\r\n * @example <tm-icons name='icon-clear'></tm-icons>\r\n */\r\nexport default {\r\n\tprops: {\r\n\t\tdense: {\r\n\t\t\t//是否小边距\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tblack: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: null\r\n\t\t},\r\n\t\tprefx: {\r\n\t\t\ttype: String, //前缀\r\n\t\t\tdefault: 'iconfont'\r\n\t\t},\r\n\t\tname: {\r\n\t\t\ttype: String, //图标名称。\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tsize: {\r\n\t\t\ttype: String | Number, //图标名称。\r\n\t\t\tdefault: 28\r\n\t\t},\r\n\t\tcolor: {\r\n\t\t\ttype: String|null, //颜色名称或者颜色值。\r\n\t\t\tdefault: 'primary'\r\n\t\t},\r\n\t\t//强制转换图标类型，不通过内置判定，解决自己引用图片svg图标时当作字体图标的错误。\r\n\t\ticonType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '' //img|icon\r\n\t\t},\r\n\t\t// 跟随主题色的改变而改变。\r\n\t\tfllowTheme: {\r\n\t\t\ttype: Boolean | String,\r\n\t\t\tdefault: false\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\ticonName: function() {\r\n\t\t\t// #ifdef APP-NVUE || APP-PLUS-NVUE\r\n\t\t\tlet fontList = require('@/tm-vuetify/scss/iconfonts/iconfont.json');\r\n\t\t\tlet name = this.name.replace('icon-', '');\r\n\r\n\t\t\t// fontList.glyphs\r\n\t\t\tlet itemIcon = fontList.glyphs.find((item, index) => {\r\n\t\t\t\treturn item.font_class == name;\r\n\t\t\t});\r\n\t\t\treturn eval('\"\\\\u' + itemIcon.unicode + '\"');\r\n\t\t\t// #endif\r\n\t\t\treturn this.name;\r\n\t\t},\r\n\t\tprefx_computed() {\r\n\t\t\tlet prefix = this.name.split('-')[0];\r\n\t\t\tif (prefix == 'icon') return 'iconfont';\r\n\t\t\tif (prefix == 'mdi') return 'mdi';\r\n\r\n\t\t\treturn prefix;\r\n\t\t},\r\n\t\tblack_tmeme: function() {\r\n\t\t\treturn this.$tm.vx.state().tmVuetify.black;\r\n\t\t},\r\n\t\tvtype: function() {\r\n\t\t\tif (this.name[0] == '.' || this.name[0] == '/' || this.name.substring(0, 4) == 'http' || this.name.substring(0, 5) == 'https' || this.name.substring(0, 3) == 'ftp') {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\t\tsizes: function() {\r\n\t\t\tif (typeof this.size === 'string') {\r\n\t\t\t\tif (/[rpx|upx|rem|em|vx|vh|px]$/.test(this.size)) {\r\n\t\t\t\t\treturn this.size;\r\n\t\t\t\t}\r\n\t\t\t\treturn uni.upx2px(parseInt(this.size)) + 'px';\r\n\t\t\t}\r\n\t\t\tif (typeof this.size === 'number' && !isNaN(this.size)) {\r\n\t\t\t\treturn uni.upx2px(this.size) + 'px';\r\n\t\t\t}\r\n\t\t},\r\n\t\tcolor_tmeme: function() {\r\n\t\t\tif (this.$tm.vx.state().tmVuetify.color !== null && this.$tm.vx.state().tmVuetify.color && this.fllowTheme) {\r\n\t\t\t\treturn this.$tm.vx.state().tmVuetify.color;\r\n\t\t\t}\r\n\t\t\treturn this.color;\r\n\t\t},\r\n\t\tcolors: {\r\n\t\t\tget: function() {\r\n\t\t\t\tif (!this.color_tmeme) return 'text-primary';\r\n\t\t\t\tif (!this.$TestColor(this.color_tmeme)) {\r\n\t\t\t\t\treturn this.color_tmeme;\r\n\t\t\t\t}\r\n\t\t\t\treturn 'text-' + this.color_tmeme;\r\n\t\t\t},\r\n\t\t\tset: function() {\r\n\t\t\t\tif (!this.$TestColor(this.color_tmeme)) {\r\n\t\t\t\t\tthis.colorTheme = false;\r\n\t\t\t\t\treturn this.color_tmeme;\r\n\t\t\t\t}\r\n\t\t\t\tthis.colorTheme = true;\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcolorTheme: true\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\tonclick(e) {\r\n\t\t\tthis.$emit('click', e);\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tm-icons {\r\n\tdisplay: inline-block;\r\n\t.tm-icons-item{\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t.icons {\r\n\t\t&.black {\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n}\r\n</style>\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-icons.vue?vue&type=style&index=0&id=7b090d4d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-icons.vue?vue&type=style&index=0&id=7b090d4d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775113\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}