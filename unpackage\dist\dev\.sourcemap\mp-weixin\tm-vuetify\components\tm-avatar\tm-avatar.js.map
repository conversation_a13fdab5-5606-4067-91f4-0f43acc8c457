{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-avatar/tm-avatar.vue?ffe8", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-avatar/tm-avatar.vue?155e", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-avatar/tm-avatar.vue?dada", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-avatar/tm-avatar.vue?ff0e", "uni-app:///tm-vuetify/components/tm-avatar/tm-avatar.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-avatar/tm-avatar.vue?3f78", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-avatar/tm-avatar.vue?69d9"], "names": ["components", "tmBadges", "name", "props", "size", "type", "default", "color", "dotColor", "customClass", "shadow", "label", "fontSize", "src", "titl", "black", "round", "text", "outlined", "dot", "dotPos", "border", "fllowTheme", "data", "imgstyle", "width", "height", "wkstyle", "computed", "fontsize", "black_tmeme", "color_tmeme", "configStyle", "get", "set", "mounted", "methods", "setConfigStyle", "onclick", "event"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACfA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCyDjrB;EACAA;IAAAC;EAAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IAEAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;EACA;EACAiB;IACA;MACAC;QAAAC;QAAAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACAC;QACA;MACA;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAV;MACAC;IACA;EACA;EACAU;IACAC;MACA;IACA;IACAC;MACA;QAAAC;QAAA1B;QAAAF;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvLA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-avatar/tm-avatar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-avatar.vue?vue&type=template&id=746686ae&scoped=true&\"\nvar renderjs\nimport script from \"./tm-avatar.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-avatar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-avatar.vue?vue&type=style&index=0&id=746686ae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"746686ae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-avatar/tm-avatar.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-avatar.vue?vue&type=template&id=746686ae&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  _vm.$initSSP()\n  if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n    _vm.$setSSP(\"default\", {\n      src: _vm.src,\n    })\n  }\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-avatar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-avatar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t\t@click=\"onclick\"\r\n\t\t:style=\"configStyle\"\r\n\t\tclass=\"tm--avatar d-inline-block  \"\r\n\t\t:class=\"[titl ? 'round-2' : 'rounded', text ? '' : `shadow-${color_tmeme}-${shadow}`, customClass]\"\r\n\t>\r\n\t\t<view class=\"tm--avatar--dot\" :class=\"[dotPos == 'top' ? 'top' : '', dotPos == 'bottom' ? 'bottom' : '']\">\r\n\t\t\t<slot name=\"dot\">\r\n\t\t\t\t<view v-if=\"dotPos == 'bottom'\" style=\"width: 100%;\"><tm-badges :offset=\"[0, -10]\" v-if=\"dot\" :color=\"dotColor\"></tm-badges></view>\r\n\t\t\t\t<tm-badges :offset=\"[2, -2]\" v-if=\"dot && dotPos == 'top'\" :color=\"dotColor\"></tm-badges>\r\n\t\t\t</slot>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t\tclass=\"flex-center overflow text-align-center tm--avatar--conter\"\r\n\t\t\t:class=\"[\r\n\t\t\t\ttitl ? `round-${round}` : 'rounded',\r\n\t\t\t\t!label && !src ? color_tmeme : '',\r\n\t\t\t\tlabel ? color_tmeme : '',\r\n\t\t\t\tblack_tmeme ? 'bk' : '',\r\n\t\t\t\ttext ? 'text' : '',\r\n\t\t\t\toutlined ? 'outlined' : '',\r\n\t\t\t\t`border-${color_tmeme}-a-${border}`\r\n\t\t\t]\"\r\n\t\t\t:style=\"{ width: imgstyle.width, height: imgstyle.height }\"\r\n\t\t>\r\n\t\t\t<slot name=\"default\" :src=\"src\">\r\n\t\t\t\t<image v-if=\"!label\" :class=\"[titl ? 'round-0' : 'rounded']\" :style=\"{ width: imgstyle.width, height: imgstyle.height }\" :src=\"src\"></image>\r\n\t\t\t\t<text v-if=\"label\" :style=\"{ fontSize: fontsize }\">{{ label }}</text>\r\n\t\t\t</slot>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * 头像框\r\n * @property {Number | String} size = [98|80|64] 默认：98，头像的宽高,单位upx\r\n * @property {String} color = [primary] 默认：primary，主题背景色\r\n * @property {Number|String} shadow = [] 默认：0，投影\r\n * @property {Number} round = [] 默认：0，圆角，只有在titl下起作用。\r\n * @property {String} label = [] 默认：''，当填入信息时，文本头像，禁用img模式。\r\n * @property {String} font-size = [] 默认：'36'，文字大小，单位upx，label时启用。\r\n * @property {String} src = [] 默认：'https://picsum.photos/200'，头像图片地址，label时禁用用。\r\n * @property {Boolean} titl = [true|false] 默认：false，开户titl模式即正常的正方形而非圆形。\r\n * @property {Boolean} text = [true|false] 默认：false，文本模式\r\n * @property {Boolean} outlined = [true|false] 默认：false，边框模式\r\n * @property {Boolean} dot = [true|false] 默认：false，显示头像点。建议自行通过slot dot 自行设置。\r\n * @property {String} dot-color = [] 默认：primary，角标颜色\r\n * @property {String} dot-pos = [top|bottom] 默认：top，解析的位置\r\n * @property {Number|String} border = [] 默认：0，边框，边框颜色为你的color颜色\r\n * @property {String | Boolean} black = [true|false] 默认：false，是否开启暗黑模式\r\n * @property {String} custom-class = [] 默认：''，自定义类。\r\n * @property {Function} click 返回：{event,src,label})。\r\n * @example <tm-avatar ></tm-avatar>\r\n */\r\nimport tmBadges from '@/tm-vuetify/components/tm-badges/tm-badges.vue';\r\nexport default {\r\n\tcomponents: { tmBadges },\r\n\tname: 'tm-avatar',\r\n\tprops: {\r\n\t\t// 头像的宽高upx\r\n\t\tsize: {\r\n\t\t\ttype: Number | String,\r\n\t\t\tdefault: 98\r\n\t\t},\r\n\t\t// 主题背景色\r\n\t\tcolor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'primary'\r\n\t\t},\r\n\t\tdotColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'red'\r\n\t\t},\r\n\t\t// 自定义类\r\n\t\tcustomClass: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 投影\r\n\t\tshadow: {\r\n\t\t\ttype: Number | String,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 当填入信息时，禁用img模式。\r\n\t\tlabel: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 单位upx\r\n\t\tfontSize: {\r\n\t\t\ttype: String | Number,\r\n\t\t\tdefault: 36\r\n\t\t},\r\n\t\t// 注意，只有当label没有填写时才会启用。\r\n\t\tsrc: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'https://picsum.photos/200'\r\n\t\t},\r\n\t\t// 开户til模式即正常的正方形而非圆形。\r\n\t\ttitl: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tblack: {\r\n\t\t\ttype: Boolean | String,\r\n\t\t\tdefault: null\r\n\t\t},\r\n\t\tround: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\ttext: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\r\n\t\toutlined: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tdot: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tdotPos: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'top'\r\n\t\t},\r\n\t\tborder: {\r\n\t\t\ttype: Number | String,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 跟随主题色的改变而改变。\r\n\t\tfllowTheme: {\r\n\t\t\ttype: Boolean | String,\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\timgstyle: { width: 0, height: 0 },\r\n\t\t\twkstyle: {}\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\tfontsize: function() {\r\n\t\t\treturn uni.upx2px(this.fontSize) + 'px';\r\n\t\t},\r\n\t\tblack_tmeme: function() {\r\n\t\t\tif (this.black !== null) return this.black;\r\n\t\t\treturn this.$tm.vx.state().tmVuetify.black;\r\n\t\t},\r\n\t\tcolor_tmeme: function() {\r\n\t\t\tif (this.$tm.vx.state().tmVuetify.color !== null && this.$tm.vx.state().tmVuetify.color && this.fllowTheme) {\r\n\t\t\t\treturn this.$tm.vx.state().tmVuetify.color;\r\n\t\t\t}\r\n\t\t\treturn this.color;\r\n\t\t},\r\n\t\tconfigStyle: {\r\n\t\t\tget: function() {\r\n\t\t\t\treturn this.wkstyle;\r\n\t\t\t},\r\n\t\t\tset: function(obj) {\r\n\t\t\t\tthis.wkstyle = uni.$tm.objToString(obj);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.imgstyle = {\r\n\t\t\twidth: uni.upx2px(parseInt(this.size)) + 'px',\r\n\t\t\theight: uni.upx2px(parseInt(this.size)) + 'px'\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\tsetConfigStyle(val) {\r\n\t\t\tthis.configStyle = val;\r\n\t\t},\r\n\t\tonclick(e) {\r\n\t\t\tthis.$emit('click', { event: e, src: this.src, label: this.label });\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tm--avatar {\r\n\tposition: relative;\r\n\tline-height: 0;\r\n\tvertical-align: middle;\r\n\t.tm--avatar--dot {\r\n\t\tposition: absolute;\r\n\t\tz-index: 10;\r\n\t\twidth: 100%;\r\n\t\t&.bottom {\r\n\t\t\tbottom: 0upx;\r\n\t\t}\r\n\t}\r\n\t.tm--avatar--conter {\r\n\t\tline-height: 0;\r\n\t}\r\n}\r\n</style>\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-avatar.vue?vue&type=style&index=0&id=746686ae&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-avatar.vue?vue&type=style&index=0&id=746686ae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775127\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}