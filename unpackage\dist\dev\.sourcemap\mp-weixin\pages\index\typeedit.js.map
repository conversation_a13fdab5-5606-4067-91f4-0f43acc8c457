{"version": 3, "sources": ["webpack:///D:/source/bill-view/pages/index/typeedit.vue?ebf9", "webpack:///D:/source/bill-view/pages/index/typeedit.vue?fc69", "uni-app:///main.js", "webpack:///D:/source/bill-view/pages/index/typeedit.vue?6ba2", "webpack:///D:/source/bill-view/pages/index/typeedit.vue?c9a9", "webpack:///D:/source/bill-view/pages/index/typeedit.vue?8d8f", "webpack:///D:/source/bill-view/pages/index/typeedit.vue?b84a", "uni-app:///pages/index/typeedit.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tmMenubars", "tmTabs", "tmSheet", "tmGrid", "tmIcons", "tmInput", "tmButton", "tmPoup", "tmForm", "tmSwitchList", "tmDragList", "data", "list", "title", "activeIndex", "typeList", "showModal", "currentType", "icon", "text", "color", "type", "actions", "width", "iconList", "fontColor", "onLoad", "methods", "goBack", "uni", "changeType", "loadTypeList", "console", "editType", "deleteType", "content", "success", "showAddModal", "selectIcon", "submitForm", "handleAction", "dragChange", "categoryId", "sortNum"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAgvC,CAAgB,isCAAG,EAAC,C;;;;;;;;;;;ACApwC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC2K;AAC3K,gBAAgB,qLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAA6oB,CAAgB,0pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4EjqB;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC,OACA;QAAAC;MAAA,GACA;QAAAA;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAH;QACAI;QACAH;MACA,GACA;QACAD;QACAI;QACAH;MACA;MACAI,WACA;QAAAN;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA,GACA;QAAAP;QAAAC;QAAAC;QAAAK;MAAA;IAEA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACAC;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACAL;QACAhB;QACAsB;QACAC;UACA;YACA;cACA;gBACAP;gBACA;cACA;gBACAA;cACA;YACA;UACA;QACA;MACA;IACA;IACAQ;MACA;QACAnB;QACAC;QACAC;QACAC;MACA;MACA;IACA;IACAiB;MACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAV;QACA;MACA;MAEA;QACA;UACAA;UACA;UACA;QACA;UACAA;QACA;MACA;IACA;IACAW;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACAT;MACA;MACAA;MACA;QAAA;UACAU;UACAC;UACAtB;QACA;MAAA;MACA;MACA;QACA;UACA;UACA;UACAQ;QACA;UACAA;QACA;MACA;QACAA;MACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/index/typeedit.js", "sourcesContent": ["import mod from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./typeedit.vue?vue&type=style&index=0&id=4e037d1a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./typeedit.vue?vue&type=style&index=0&id=4e037d1a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775128\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/typeedit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./typeedit.vue?vue&type=template&id=4e037d1a&scoped=true&\"\nvar renderjs\nimport script from \"./typeedit.vue?vue&type=script&lang=js&\"\nexport * from \"./typeedit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./typeedit.vue?vue&type=style&index=0&id=4e037d1a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4e037d1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/typeedit.vue\"\nexport default component.exports", "export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./typeedit.vue?vue&type=template&id=4e037d1a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$tm.vx.state()\n  var m0 = _vm.$hasSSP(\"726ca72c-4\")\n  var m1 = m0 ? _vm.$getSSP(\"726ca72c-4\", \"default\") : null\n  var m2 = m0 ? _vm.$getSSP(\"726ca72c-4\", \"default\") : null\n  var m3 = m0 ? _vm.$getSSP(\"726ca72c-4\", \"default\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function (e) {\n      return _vm.handleAction(e, _vm.$getSSP(\"726ca72c-4\", \"default\")[\"item\"])\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./typeedit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./typeedit.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- <view class=\"container\">\r\n\t\t<tm-menubars color=\"primary\" title=\"分类管理\" :shadow=\"0\">\r\n\t\t\t<template #left>\r\n\t\t\t\t<view class=\"pl-24\">\r\n\t\t\t\t\t<tm-button @click=\"goBack\" theme=\"bg-gradient-orange-accent\" size=\"S\">返回</tm-button>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</tm-menubars> -->\r\n\t<view :style=\"{ minHeight: sys.windowHeight + 'px' }\"\r\n\t\t:class=\"[$tm.vx.state().tmVuetify.black ? 'black' : 'grey text ']\">\r\n\t\t<tm-menubars color=\"primary\" title=\"分类管理\" :shadow=\"0\">\r\n\t\t\t<template #left>\r\n\t\t\t\t<view class=\"pl-24\">\r\n\t\t\t\t\t<tm-button @click=\"goBack\" theme=\"bg-gradient-orange-accent\" size=\"S\"></tm-button>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</tm-menubars>\r\n\t\t<view>\r\n\t\t\t<view class=\"fixed fulled  overflow\" style=\"z-index: 8;\">\r\n\t\t\t\t<tm-tabs :fllowTheme=\"false\" bg-color=\"amber\" color=\"red\" font-size=\"38\" active-font-size=\"38\"  @change=\"changeType\" v-model=\"activeIndex\"\r\n\t\t\t\t\t:list=\"list\" range-key=\"title\"></tm-tabs>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"height: 84rpx;\"></view>\r\n\t\t\r\n\t\t\t\t\r\n\t\t\t<view class=\"type-list\">\r\n\t\t\t\t<tm-dragList :list=\"typeList\" @change=\"dragChange\">\r\n\t\t\t\t\t<template v-slot=\"{item}\">\r\n\t\t\t\t\t\t<tm-switchList \r\n\t\t\t\t\t\t\tcolor=\"blue\" \r\n\t\t\t\t\t\t\t:icon=\"item.icon\"\r\n\t\t\t\t\t\t\t:title=\"item.text\"\r\n\t\t\t\t\t\t\t:label=\"item.color\"\r\n\t\t\t\t\t\t\t:actions=\"actions\"\r\n\t\t\t\t\t\t\t@actionsClick=\"(e) => handleAction(e, item)\"\r\n\t\t\t\t\t\t\t:rightIcon=\"'icon-drag'\"\r\n\t\t\t\t\t\t></tm-switchList>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</tm-dragList>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"add-btn\">\r\n\t\t\t\t<tm-button @click=\"showAddModal\" theme=\"bg-gradient-green-accent\" block>新增分类</tm-button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 编辑/新增弹窗 -->\r\n\t\t<tm-poup v-model=\"showModal\" position=\"bottom\" height=\"80vh\">\r\n\t\t\t<view class=\"modal-content\">\r\n\t\t\t\t<tm-form @submit=\"submitForm\" ref=\"formData\">\r\n\t\t\t\t\t<tm-sheet color=\"blue text\" :shadow=\"24\">\r\n\t\t\t\t\t\t<tm-input name=\"text\" required title=\"名称\" v-model=\"currentType.text\" :left-icon=\"currentType.icon\"></tm-input>\r\n\t\t\t\t\t\t<tm-grid :grid=\"5\" @change=\"selectIcon\" color=\"amber\" :list=\"iconList\"></tm-grid>\r\n\t\t\t\t\t\t<tm-input name=\"color\" required title=\"颜色\" v-model=\"currentType.color\"></tm-input>\r\n\t\t\t\t\t</tm-sheet>\r\n\t\t\t\t\t<tm-button navtie-type=\"form\" theme=\"bg-gradient-blue-accent\" block>保存</tm-button>\r\n\t\t\t\t</tm-form>\r\n\t\t\t</view>\r\n\t\t</tm-poup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport tmMenubars from '@/tm-vuetify/components/tm-menubars/tm-menubars.vue';\r\nimport tmTabs from \"@/tm-vuetify/components/tm-tabs/tm-tabs.vue\"\r\nimport tmSheet from \"@/tm-vuetify/components/tm-sheet/tm-sheet.vue\"\r\nimport tmGrid from \"@/tm-vuetify/components/tm-grid/tm-grid.vue\"\r\nimport tmIcons from '@/tm-vuetify/components/tm-icons/tm-icons.vue';\r\nimport tmInput from \"@/tm-vuetify/components/tm-input/tm-input.vue\"\r\nimport tmButton from \"@/tm-vuetify/components/tm-button/tm-button.vue\"\r\nimport tmPoup from \"@/tm-vuetify/components/tm-poup/tm-poup.vue\"\r\nimport tmForm from \"@/tm-vuetify/components/tm-form/tm-form.vue\"\r\nimport tmSwitchList from '@/tm-vuetify/components/tm-switchList/tm-switchList.vue';\r\nimport tmDragList from '@/tm-vuetify/components/tm-dragList/tm-dragList.vue';\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\ttmMenubars,\r\n\t\ttmTabs,\r\n\t\ttmSheet,\r\n\t\ttmGrid,\r\n\t\ttmIcons,\r\n\t\ttmInput,\r\n\t\ttmButton,\r\n\t\ttmPoup,\r\n\t\ttmForm,\r\n\t\ttmSwitchList,\r\n\t\ttmDragList\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tlist: [\r\n\t\t\t\t{title: '支出'},\r\n\t\t\t\t{title: '收入'}\r\n\t\t\t],\r\n\t\t\tactiveIndex: 0,\r\n\t\t\ttypeList: [],\r\n\t\t\tshowModal: false,\r\n\t\t\tcurrentType: {\r\n\t\t\t\ticon: '',\r\n\t\t\t\ttext: '',\r\n\t\t\t\tcolor: '',\r\n\t\t\t\ttype: 0\r\n\t\t\t},\r\n\t\t\tactions: [{\r\n\t\t\t\ttext: \"编辑\",\r\n\t\t\t\twidth: 140,\r\n\t\t\t\tcolor: 'black'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\ttext: \"删除\",\r\n\t\t\t\twidth: 110,\r\n\t\t\t\tcolor: 'red'\r\n\t\t\t}],\r\n\t\t\ticonList: [\r\n\t\t\t\t{icon:'myicon-jucan',text:'聚会',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-caiying',text:'餐饮',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-fushi',text:'服饰',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-gouwu',text:'购物',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-gongzi',text:'工资',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-youfei',text:'油费',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-shuifei',text:'水费',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-dianfei',text:'电费',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-touzi',text:'投资',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-huazhuangpin',text:'化妆品',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-kanbing',text:'看病',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-qita',text:'其他',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-waibao',text:'外包',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-hongbao',text:'红包',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-gouwuche',text:'购物车',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-xiaopaopao',text:'小泡泡',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-qiche',text:'汽车',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-wanju',text:'玩具',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-naifen',text:'奶粉',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-niaobushi',text:'尿布湿',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-shuiguo',text:'水果',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-lingshi',text:'零食',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-jiaotong',text:'交通',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-yimiao',text:'疫苗',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-shenghuo',text:'生活',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-baitiao',text:'白条',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-shoushi',text:'首饰',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-shucai',text:'蔬菜',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-yanjiu',text:'研究',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-zhangbei',text:'账本',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-shuma',text:'数码',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-yundong',text:'运动',color:'',fontColor:''},\r\n\t\t\t\t{icon:'myicon-tongxun',text:'通讯',color:'',fontColor:''}\r\n\t\t\t]\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\t// 接收传入的 er 参数\r\n\t\tif (options.er) {\r\n\t\t\t// 根据 er 值设置对应的 tab\r\n\t\t\tthis.activeIndex = options.er === '0' ? 0 : 1;\r\n\t\t}\r\n\t\tthis.loadTypeList()\r\n\t},\r\n\tmethods: {\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack()\r\n\t\t},\r\n\t\tchangeType(e) {\r\n\t\t\tthis.activeIndex = e\r\n\t\t\tthis.loadTypeList()\r\n\t\t},\r\n\t\tloadTypeList() {\r\n\t\t\tthis.$api.getTypeList(this.activeIndex).then(res => {\r\n\t\t\t\tif(res.data.success) {\r\n\t\t\t\t\tthis.typeList = res.data.result\r\n\t\t\t\t\tconsole.log(this.typeList)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\teditType(item) {\r\n\t\t\tthis.currentType = {...item}\r\n\t\t\tthis.showModal = true\r\n\t\t},\r\n\t\tdeleteType(item) {\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确定要删除该分类吗？',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.$api.deleteType(item.id).then(res => {\r\n\t\t\t\t\t\t\tif(res.data.success) {\r\n\t\t\t\t\t\t\t\tuni.$tm.toast('删除成功')\r\n\t\t\t\t\t\t\t\tthis.loadTypeList()\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.$tm.toast(res.data.message)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tshowAddModal() {\r\n\t\t\tthis.currentType = {\r\n\t\t\t\ticon: '',\r\n\t\t\t\ttext: '',\r\n\t\t\t\tcolor: '',\r\n\t\t\t\ttype: this.activeIndex\r\n\t\t\t}\r\n\t\t\tthis.showModal = true\r\n\t\t},\r\n\t\tselectIcon(e) {\r\n\t\t\tthis.currentType.icon = e.data.icon\r\n\t\t\tif (!this.currentType.text || this.currentType.text.trim() === '') {\r\n\t\t\t\tthis.currentType.text = e.data.text\r\n\t\t\t}\r\n\t\t},\r\n\t\tsubmitForm(e) {\r\n\t\t\tif (e === false) {\r\n\t\t\t\tuni.$tm.toast(\"请填写必填项\")\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.$api.saveType(this.currentType).then(res => {\r\n\t\t\t\tif(res.data.success) {\r\n\t\t\t\t\tuni.$tm.toast('保存成功')\r\n\t\t\t\t\tthis.showModal = false\r\n\t\t\t\t\tthis.loadTypeList()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.$tm.toast(res.data.message)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\thandleAction(e, item) {\r\n\t\t\tif (e.index === 0) {\r\n\t\t\t\t// 编辑\r\n\t\t\t\tthis.editType(item)\r\n\t\t\t} else {\r\n\t\t\t\t// 删除\r\n\t\t\t\tthis.deleteType(item)\r\n\t\t\t}\r\n\t\t},\r\n\t\tdragChange(e) {\r\n\t\t\t// 创建一个新数组来存储排序后的列表，避免直接修改原数组\r\n\t\t\tconsole.log(e)\r\n\t\t\tconst newList = [...e]\r\n\t\t\tconsole.log(newList)\r\n\t\t\tlet params = newList.map((item, index) => ({\r\n\t\t\t\t\tcategoryId: item.id,\r\n\t\t\t\t\tsortNum: index,\r\n\t\t\t\t\ttype: this.activeIndex,\r\n\t\t\t\t}))\r\n\t\t\t// 调用后端接口保存新的排序\r\n\t\t\tthis.$api.updateTypeSort(params).then(res => {\r\n\t\t\t\tif(res.data.success) {\r\n\t\t\t\t\t// 只有在保存成功后才更新本地列表\r\n\t\t\t\t\tthis.typeList = newList\r\n\t\t\t\t\tuni.$tm.toast('排序已更新')\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.$tm.toast(res.data.message)\r\n\t\t\t\t}\r\n\t\t\t}).catch(err => {\r\n\t\t\t\tuni.$tm.toast('排序更新失败')\r\n\t\t\t})\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n}\r\n\r\n.content {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.type-list {\r\n\tmargin-top: 20rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.add-btn {\r\n\tmargin-top: 40rpx;\r\n}\r\n\r\n.modal-content {\r\n\tpadding: 40rpx;\r\n}\r\n</style> "], "sourceRoot": ""}