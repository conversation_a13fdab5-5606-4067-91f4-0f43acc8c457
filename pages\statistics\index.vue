<template>
	<view :style="{ minHeight: sys.windowHeight + 'px' }"
		:class="[$tm.vx.state().tmVuetify.black ? 'black' : 'grey-lighten-5']">
		<tm-menubars color="primary" title="统计分析" :shadow="0">
		</tm-menubars>

		<!-- 主要切换标签 - 年度/月度 -->
		<view class="main-tabs-container">
			<tm-tabs :fllowTheme="false" bg-color="amber"
				font-size="36" active-font-size="36" @change="changeTab"
				v-model="activeTabIndex" :list="tabList" range-key="title" :shadow="0">
			</tm-tabs>
		</view>

		<!-- 时间选择器 -->
		<view class="time-selector"
			:class="[$tm.vx.state().tmVuetify.black ? 'grey-darken-5 bk' : 'white']">
			<view class="flex flex-center px-32 py-24">
				<!-- 年度选择器 - 年度tab时显示 -->
				<view v-if="activeTabIndex === 0" class="time-picker-item">
					<tm-pickersDate @confirm="selectYear" @input="changeValue"
						:showDetail="{year:true,month:false,day:false,hour:false,min:false,sec:false}">
						<text class="text-primary text-size-m">📅 {{ currentYear }}年</text>
					</tm-pickersDate>
				</view>
				<!-- 月度选择器 - 月度tab时显示 -->
				<view v-if="activeTabIndex === 1" class="time-picker-item">
					<tm-pickersDate @confirm="selectTime" @input="changeValue"
						:showDetail="{year:true,month:true,day:false,hour:false,min:false,sec:false}">
						<text class="text-primary text-size-m">📅 {{ time }}</text>
					</tm-pickersDate>
				</view>
			</view>
		</view>

		<!-- 收支概览卡片 - 仅在月度tab显示 -->
		<view v-if="activeTabIndex === 1" class="overview-cards mx-24 mt-24">
			<tm-sheet bg-color="amber" :shadow="2" round="12" :padding="[24,20]">
				<view class="flex flex-between">
					<view class="overview-item">
						<view class="text-size-xs text-grey-darken-1">总收入</view>
						<view class="text-size-l text-green font-weight-bold mt-8">
							¥{{ totalIncome.toFixed(2) }}
						</view>
					</view>
					<view class="overview-item">
						<view class="text-size-xs text-grey-darken-1">总支出</view>
						<view class="text-size-l text-red font-weight-bold mt-8">
							¥{{ totalExpense.toFixed(2) }}
						</view>
					</view>
					<view class="overview-item">
						<view class="text-size-xs text-grey-darken-1">净收入</view>
						<view class="text-size-l font-weight-bold mt-8"
							:class="netIncome >= 0 ? 'text-green' : 'text-red'">
							¥{{ netIncome.toFixed(2) }}
						</view>
					</view>
				</view>
			</tm-sheet>
		</view>

		<!-- 图表内容区域 -->
		<view class="charts-container mx-24 mt-16">
			<!-- 年度视图 -->
			<view v-if="activeTabIndex === 0">
				<!-- 收支类型切换 -->
				<view class="expense-type-tabs mb-24">
					<tm-sheet :fllowTheme="false" bg-color="amber" :shadow="1" round="8" :padding="[8,8]">
						<view class="flex">
							<view
								v-for="(item, index) in expenseTypeList"
								:key="index"
								class="expense-type-item"
								:class="{ 'active': currentExpenseType === index }"
								@click="changeExpenseType(index)">
								<text>{{ item }}</text>
							</view>
						</view>
					</tm-sheet>
				</view>

				<!-- 年度趋势图 -->
				<tm-sheet :fllowTheme="false" bg-color="amber" :shadow="2" round="12" :padding="[24,20]" class="mb-24">
					<view class="chart-header">
						<view class="flex flex-between flex-middle">
							<view>
								<view class="text-size-m font-weight-bold">{{ currentYear }}年{{ expenseTypeList[currentExpenseType] }}趋势</view>
								<view class="text-size-xs text-grey-darken-1 mt-8">全年12个月数据变化</view>
							</view>
							<view class="yearly-stats" v-if="yearlyStats.totalAmount > 0">
								<view class="stat-item">
									<view class="stat-label">年度总额</view>
									<view class="stat-value" :class="currentExpenseType === 0 ? 'text-red' : 'text-green'">
										¥{{ yearlyStats.totalAmount.toFixed(2) }}
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="charts-box" style="height: 350px;">
						<!-- 当有数据时显示趋势图 -->
						<qiun-data-charts
							v-if="yearlyTrendData.series && yearlyTrendData.series.length > 0 && yearlyTrendData.series[0].data && yearlyTrendData.series[0].data.length > 0"
							type="line"
							:chartData="yearlyTrendData"
							:style="!showCanvas?'display:none':''" />
						<!-- 当没有数据时显示空状态 -->
						<view v-else class="empty-state">
							<view class="empty-icon">📈</view>
							<view class="empty-text">暂无{{ currentYear }}年{{ expenseTypeList[currentExpenseType] }}数据</view>
						</view>
					</view>
				</tm-sheet>
			</view>

			<!-- 月度视图 -->
			<view v-if="activeTabIndex === 1">
				<!-- 收支类型切换 -->
				<view class="expense-type-tabs mb-24">
					<tm-sheet bg-color="amber" :shadow="1" round="8" :padding="[8,8]">
						<view class="flex">
							<view
								v-for="(item, index) in expenseTypeList"
								:key="index"
								class="expense-type-item"
								:class="{ 'active': currentExpenseType === index }"
								@click="changeExpenseType(index)">
								<text>{{ item }}</text>
							</view>
						</view>
					</tm-sheet>
				</view>

				<!-- 类别排行柱状图 -->
				<tm-sheet bg-color="amber" :shadow="2" round="12" :padding="[24,20]" class="mb-24">
					<view class="chart-header">
						<view class="text-size-m font-weight-bold">{{ expenseTypeList[currentExpenseType] }}类别排行</view>
						<view class="text-size-xs text-grey-darken-1 mt-8">{{ time }}月数据</view>
					</view>
					<view class="charts-box" style="height: 300px;">
						<!-- 当有数据时显示柱状图 -->
						<qiun-data-charts
							v-if="categoryRankData.series && categoryRankData.series.length > 0 && categoryRankData.series[0].data && categoryRankData.series[0].data.length > 0"
							type="column"
							:chartData="categoryRankData"
							:style="!showCanvas?'display:none':''" />
						<!-- 当没有数据时显示空状态 -->
						<view v-else class="empty-state">
							<view class="empty-icon">📊</view>
							<view class="empty-text">暂无{{ expenseTypeList[currentExpenseType] }}数据</view>
						</view>
					</view>
				</tm-sheet>

				<!-- 月度饼图 -->
				<tm-sheet bg-color="amber" :shadow="2" round="12" :padding="[24,20]" class="mb-24">
					<view class="chart-header">
						<view class="text-size-m font-weight-bold">{{ time }}月{{ expenseTypeList[currentExpenseType] }}分布</view>
						<view class="text-size-xs text-grey-darken-1 mt-8">{{ month }}</view>
					</view>
					<view class="charts-box" style="height: 300px;">
						<!-- 当有数据时显示饼图 -->
						<qiun-data-charts
							v-if="monthData.series && monthData.series.length > 0 && monthData.series[0].data && monthData.series[0].data.length > 0"
							type="pie"
							:chartData="monthData"
							:style="!showCanvas?'display:none':''" />
						<!-- 当没有数据时显示空状态 -->
						<view v-else class="empty-state">
							<view class="empty-icon">📊</view>
							<view class="empty-text">暂无{{ expenseTypeList[currentExpenseType] }}数据</view>
						</view>
					</view>
				</tm-sheet>

				<!-- 日度饼图 -->
				<tm-sheet bg-color="amber" :shadow="2" round="12" :padding="[24,20]" class="mb-24">
					<view class="chart-header">
						<view class="text-size-m font-weight-bold">{{ formatDate(now) }}{{ expenseTypeList[currentExpenseType] }}分布</view>
						<view class="text-size-xs text-grey-darken-1 mt-8">{{ day }}</view>
					</view>
					<view class="charts-box" style="height: 300px;">
						<!-- 当有数据时显示饼图 -->
						<qiun-data-charts
							v-if="dayData.series && dayData.series.length > 0 && dayData.series[0].data && dayData.series[0].data.length > 0"
							type="pie"
							:chartData="dayData"
							:style="!showCanvas?'display:none':''" />
						<!-- 当没有数据时显示空状态 -->
						<view v-else class="empty-state">
							<view class="empty-icon">📊</view>
							<view class="empty-text">暂无{{ expenseTypeList[currentExpenseType] }}数据</view>
						</view>
					</view>
				</tm-sheet>
			</view>
		</view>
	</view>
</template>

<script>
	import tmMenubars from '@/tm-vuetify/components/tm-menubars/tm-menubars.vue';
	import tmTabs from "@/tm-vuetify/components/tm-tabs/tm-tabs.vue"
	import tmSheet from "@/tm-vuetify/components/tm-sheet/tm-sheet.vue"
	import tmPickersDate from "@/tm-vuetify/components/tm-pickersDate/tm-pickersDate.vue"
	export default {
		components: {
			tmTabs,
			tmMenubars,
			tmSheet,
			tmPickersDate,
		},
		data() {
			return {
				time: '',
				// 主要tab列表
				tabList: [{title:'年度'}, {title:'月度'}],
				activeTabIndex: 0, // 当前激活的主tab
				// 收支类型列表
				expenseTypeList: ['支出', '收入'],
				currentExpenseType: 0, // 当前选择的收支类型
				// 保留原有的数据结构以兼容现有代码
				list: ['支出', '收入'],
				activeIndex: 0,
				show: true,
				word: '',
				type: {},
				er: 0,
				times: '',
				types: [],
				month: '',
				day: '',
				charts: {},
				monthData: {},
				dayData: {},
				categoryRankData: { // 新增：类别排行柱状图数据
					categories: [],
					series: []
				},
				// 新增：年度趋势图数据
				yearlyTrendData: {
					categories: [],
					series: []
				},
				// 新增：年度统计数据
				yearlyStats: {
					totalAmount: 0,
					totalCount: 0,
					avgAmount: 0,
					maxAmount: 0,
					minAmount: 0,
					trendAnalysis: {}
				},
				currentYear: new Date().getFullYear(), // 当前选择的年份
				now: '',
				Showhiddenunits: false,
				showCanvas: true,
				// 新增：收支概览数据
				totalIncome: 0,
				totalExpense: 0,
				netIncome: 0,
				// 新增：加载状态
				loading: false,
			};
		},
		watch: {
			Showhiddenunits(newValue, oldValue) {
				this.showCanvas = !newValue
			},
		},
		created() {
			this.sys = uni.getSystemInfoSync();
			// #ifdef H5
			this.bottom = 55
			// #endif
		},
		onLoad() {
			// #ifdef MP
			this.top = uni.upx2px(150);
			// #endif

		},
		onShow() {
			this.getCurrentTime()
			this.loadDataForCurrentTab();
		},
		mounted() {
			this.getCurrentTime()
			this.judgeLogin(() => {
				this.loadType()
			});
		},
		methods: {
			// 根据当前激活的tab加载数据
			loadDataForCurrentTab() {
				// 根据当前tab加载对应数据
				if (this.activeTabIndex === 0) {
					// 年度tab - 加载年度趋势数据
					this.loadYearlyTrendData();
				} else {
					// 月度tab - 加载月度、日度和概览数据
					this.loadMonthData();
					this.loadDayData();
					this.updateOverviewData();
				}
			},
			// 格式化日期显示
			formatDate(dateStr) {
				if (!dateStr) return '';
				const parts = dateStr.split('/');
				if (parts.length === 3) {
					return `${parts[1]}月${parts[2]}日`;
				}
				return dateStr;
			},

			// 更新收支概览数据
			updateOverviewData() {
				this.judgeLogin(res => {
					// 获取当月收入数据
					this.$api.getBillStatistics({
						uid: res.id,
						er: 1, // 收入
						time: this.time
					}).then(incomeRes => {
						this.totalIncome = incomeRes.data.result.totalAmount || 0;
						this.updateNetIncome();
					});

					// 获取当月支出数据
					this.$api.getBillStatistics({
						uid: res.id,
						er: 0, // 支出
						time: this.time
					}).then(expenseRes => {
						this.totalExpense = expenseRes.data.result.totalAmount || 0;
						this.updateNetIncome();
					});
				});
			},

			// 计算净收入
			updateNetIncome() {
				this.netIncome = this.totalIncome - this.totalExpense;
			},

			// 生成模拟年度数据（用于测试）
			generateMockYearlyData() {
				// 固定使用标准的12个月标签
				const months = ['1月', '2月', '3月', '4月', '5月', '6月',
								'7月', '8月', '9月', '10月', '11月', '12月'];
				const values = [];

				// 生成随机数据用于演示
				for (let i = 0; i < 12; i++) {
					const baseAmount = this.currentExpenseType === 0 ? 3000 : 5000; // 支出基数3000，收入基数5000
					const randomVariation = Math.random() * 2000 - 1000; // ±1000的随机变化
					values.push(Math.max(0, baseAmount + randomVariation));
				}

				this.yearlyTrendData = {
					title: {
						text: '',
						left: 'center'
					},
					tooltip: {
						trigger: 'axis',
						formatter: function(params) {
							if (params && params.length > 0) {
								const param = params[0];
								const value = param.value || 0;
								return `${param.name}<br/>${param.seriesName}: ¥${value.toFixed(2)}`;
							}
							return '';
						}
					},
					legend: {
						data: [this.expenseTypeList[this.currentExpenseType] + '金额'],
						bottom: '0%'
					},
					grid: {
						left: '3%',
						right: '4%',
						bottom: '15%',
						top: '10%',
						containLabel: true
					},
					xAxis: {
						type: 'category',
						boundaryGap: false,
						data: months,
						axisLabel: {
							fontSize: 12
						}
					},
					yAxis: {
						type: 'value',
						name: '金额(元)',
						axisLabel: {
							formatter: function(value) {
								if (value >= 10000) {
									return '¥' + (value / 10000).toFixed(1) + 'w';
								} else if (value >= 1000) {
									return '¥' + (value / 1000).toFixed(1) + 'k';
								} else {
									return '¥' + value;
								}
							}
						}
					},
					series: [{
						name: this.expenseTypeList[this.currentExpenseType] + '金额',
						type: 'line',
						data: values,
						smooth: true,
						symbol: 'circle',
						symbolSize: 6,
						lineStyle: {
							width: 3,
							color: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66'
						},
						areaStyle: {
							opacity: 0.3,
							color: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66'
						},
						itemStyle: {
							color: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66',
							borderWidth: 2,
							borderColor: '#fff'
						}
					}]
				};
			},

			// 加载年度趋势数据
			loadYearlyTrendData() {
				this.judgeLogin(res => {
					this.$api.getBillYearlyTrend({
						uid: res.id,
						year: this.currentYear,
						er: this.er,
						// categoryId: '', // 可选：特定分类
						// ledgerId: ''    // 可选：特定账本
					}).then(res => {
						console.log('年度趋势数据响应:', res.data);

						// 获取返回的数据
						const result = res.data.result || {};

						// 固定使用标准的12个月标签
						const months = ['1月', '2月', '3月', '4月', '5月', '6月',
										'7月', '8月', '9月', '10月', '11月', '12月'];
						const amounts = result.amounts || [];

						// 确保amounts数组有12个元素，缺失的补0
						const values = [];
						for (let i = 0; i < 12; i++) {
							values.push(amounts[i] || 0);
						}

						console.log('处理后的月度数据:', values);

						// 保存年度统计数据
						this.yearlyStats = {
							totalAmount: result.totalAmount || 0,
							totalCount: result.totalCount || 0,
							avgAmount: result.avgAmount || 0,
							maxAmount: result.maxAmount || 0,
							minAmount: result.minAmount || 0,
							trendAnalysis: result.trendAnalysis || {}
						};

						console.log('年度统计信息:', this.yearlyStats);

						this.yearlyTrendData = {
							title: {
								text: '',
								left: 'center'
							},
							tooltip: {
								trigger: 'axis',
								formatter: function(params) {
									if (params && params.length > 0) {
										const param = params[0];
										const value = param.value || 0;
										return `${param.name}<br/>${param.seriesName}: ¥${value.toFixed(2)}`;
									}
									return '';
								}
							},
							legend: {
								data: [this.expenseTypeList[this.currentExpenseType] + '金额'],
								bottom: '0%'
							},
							grid: {
								left: '3%',
								right: '4%',
								bottom: '15%',
								top: '10%',
								containLabel: true
							},
							xAxis: {
								type: 'category',
								boundaryGap: false,
								data: result.months,
								axisLabel: {
									fontSize: 12
								}
							},
							yAxis: {
								type: 'value',
								name: '金额(元)',
								axisLabel: {
									formatter: function(value) {
										if (value >= 10000) {
											return '¥' + (value / 10000).toFixed(1) + 'w';
										} else if (value >= 1000) {
											return '¥' + (value / 1000).toFixed(1) + 'k';
										} else {
											return '¥' + value;
										}
									}
								}
							},
							series: [{
								name: this.expenseTypeList[this.currentExpenseType] + '金额',
								type: 'line',
								data: values,
								smooth: true,
								symbol: 'circle',
								symbolSize: 6,
								lineStyle: {
									width: 3,
									color: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66'
								},
								areaStyle: {
									opacity: 0.3,
									color: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66'
								},
								itemStyle: {
									color: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66',
									borderWidth: 2,
									borderColor: '#fff'
								}
							}]
						};
					}).catch(err => {
						console.error('加载年度趋势数据失败:', err);
						console.log('使用模拟数据作为回退方案');
						// 使用模拟数据作为回退方案
						this.generateMockYearlyData();
					});
				});
			},

			changeValue(val) {
				// this.Showhiddenunits = val;
			},

			loadDayData() {
				this.judgeLogin(res => {
					this.$api.getBillStatisticsDay( {
						uid: res.id,
						er: this.er,
						time: this.now
					}).then(res => {
						const statistics = res.data.result.statistics || [];
						let data = {
							title: {
								subtext: '',
								left: 'center'
							},
							tooltip: {
								trigger: 'item',
								formatter: '{a} <br/>{b}: {c} ({d}%)'
							},
							legend: {
								orient: 'horizontal',
								bottom: '0%',
								left: 'center'
							},
							series: [{
								name: '日统计',
								type: 'pie',
								radius: ['20%', '70%'],
								center: ['50%', '45%'],
								data: statistics,
								emphasis: {
									itemStyle: {
										shadowBlur: 10,
										shadowOffsetX: 0,
										shadowColor: 'rgba(0, 0, 0, 0.5)'
									}
								},
								label: {
									show: true,
									formatter: '{b}: ¥{c}'
								}
							}]
						}
						let money = res.data.result.totalAmount || 0;
						this.day = this.list[this.er] + money.toFixed(2) + '元'
						this.dayData = data;
					}).catch(err => {
						console.error('加载日统计数据失败:', err);
						// 设置空数据状态
						this.dayData = {
							series: []
						};
						this.day = this.list[this.er] + '0.00元';
					})
				})
			},
			loadMonthData() {
				this.judgeLogin(res => {
					this.$api.getBillStatistics({
						uid: res.id,
						er: this.er,
						time: this.time
					}).then(res => {
						const billByCategory = res.data.result.billByCategory || [];
						let data = {
							title: {
								subtext: '',
								left: 'center'
							},
							tooltip: {
								trigger: 'item',
								formatter: '{a} <br/>{b}: {c} ({d}%)'
							},
							legend: {
								orient: 'horizontal',
								bottom: '0%',
								left: 'center'
							},
							series: [{
								name: '月统计',
								type: 'pie',
								radius: ['20%', '70%'],
								center: ['50%', '45%'],
								data: billByCategory,
								emphasis: {
									itemStyle: {
										shadowBlur: 10,
										shadowOffsetX: 0,
										shadowColor: 'rgba(0, 0, 0, 0.5)'
									}
								},
								label: {
									show: true,
									formatter: '{b}: ¥{c}'
								}
							}]
						}

						let money = res.data.result.totalAmount || 0;
						this.month = this.list[this.er] + money.toFixed(2) + '元'
						this.monthData = data;

						// 生成柱状图数据
						this.generateCategoryRankData(billByCategory);
					}).catch(err => {
						console.error('加载月统计数据失败:', err);
						// 设置空数据状态
						this.monthData = {
							series: []
						};
						this.month = this.list[this.er] + '0.00元';
						// 设置空的柱状图数据
						this.generateCategoryRankData([]);
					})
				})
			},

			// 生成类别排行柱状图数据
			generateCategoryRankData(categoryData) {
				// 检查数据是否为空或无效
				if (!categoryData || categoryData.length === 0) {
					this.categoryRankData = {
						categories: [],
						series: []
					};
					return;
				}

				// 过滤掉无效数据（value为0或undefined的项）
				const validData = categoryData.filter(item => item && item.value && item.value > 0);

				if (validData.length === 0) {
					this.categoryRankData = {
						categories: [],
						series: []
					};
					return;
				}

				// 按金额排序
				const sortedData = validData.sort((a, b) => b.value - a.value);

				// 取前10个类别
				const topCategories = sortedData.slice(0, 10);

				const categories = topCategories.map(item => item.name || '未知类别');
				const values = topCategories.map(item => item.value);

				this.categoryRankData = {
					categories: categories,
					series: [{
						name: this.list[this.er] + '金额',
						data: values,
						color: this.er === 0 ? '#ff6b6b' : '#51cf66' // 支出红色，收入绿色
					}],
					yAxis: {
						data: categories
					},
					xAxis: {
						name: '金额(元)'
					}
				};
			},
			loadType() {
				// this.$ajax.get('/type/load/' + this.er).then(res => {
				// 	this.types = res.data
				// 	this.type = this.types[0];
				// })
				this.$api.getTypeList(this.er).then(res => {
					
					if(res.data.success){
						this.types = res.data.result
						this.type = this.types[0];
					}
				})
			},
			selectType(e) {
				this.type = e.data
			},
			closeKey() {
				this.show = false;
			},
			open() {
				this.show = true;
			},
			selectTime(e) {
				this.time = e.year + "/" + e.month
				this.loadMonthData();
				this.loadDayData();
				this.updateOverviewData(); // 更新收支概览
			},

			// 选择年份
			selectYear(e) {
				this.currentYear = e.year;
				this.loadYearlyTrendData(); // 加载年度趋势数据
			},

			getCurrentTime() {
				//获取当前时间并打印
				let yy = new Date().getFullYear();
				let mm = new Date().getMonth() + 1;
				let dd = new Date().getDate();
				this.time = yy + '/' + mm;
				this.now = yy + '/' + mm + '/' + dd;
				this.currentYear = yy; // 设置当前年份
			},

			// 切换主tab（年度/月度）
			changeTab(e) {
				this.activeTabIndex = e;
				console.log('切换到tab:', this.tabList[e].title);
				this.loadDataForCurrentTab();
			},

			// 切换收支类型
			changeExpenseType(index) {
				this.currentExpenseType = index;
				this.er = index; // 保持与原有逻辑兼容
				this.activeIndex = index; // 保持与原有逻辑兼容

				console.log('切换收支类型:', this.expenseTypeList[index]);

				// 根据当前tab加载对应数据
				if (this.activeTabIndex === 0) {
					// 年度tab
					this.loadYearlyTrendData();
				} else if (this.activeTabIndex === 1) {
					// 月度tab
					this.loadType();
					this.loadMonthData();
					this.loadDayData();
				}
			},

			// 保留原有的change方法以兼容
			change(e) {
				this.changeExpenseType(e);
			},
			confirm(val) {
				// this.show = true;
				// if (val <= 0) {
				// 	uni.$tm.toast('金额无效');
				// 	return;
				// }
				// this.judgeLogin((res) => {
				// 	this.$ajax.post('/bill/save', {
				// 		type: this.type.id,
				// 		er: this.er,
				// 		value: val,
				// 		uid: res.id,
				// 		time: this.time
				// 	}).then(res => {
				// 		this.word = ''
				// 		this.type = {}
				// 	})
				// });

			}
		}
	};
</script>

<style lang="scss">
	page,
	body {
		min-height: 100%;
		background-color: #f5f5f5;
	}
</style>

<style lang="scss" scoped>
	.main-tabs-container {
		position: sticky;
		top: 0;
		z-index: 20;
		background: #667eea;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.time-selector {
		position: sticky;
		top: 84rpx; // 调整位置，避免被主tab遮挡
		z-index: 15;
		border-bottom: 1px solid #f0f0f0;
		min-height: 88rpx; // 确保容器有固定高度，避免闪动

		.time-picker-item {
			flex: 1;
			text-align: center;
			padding: 12rpx 24rpx;
			border-radius: 8rpx;
			background: rgba(102, 126, 234, 0.1);
			margin: 0 8rpx;
			border: 1px solid rgba(102, 126, 234, 0.2);
			transition: all 0.3s ease; // 添加平滑过渡

			&:hover {
				background: rgba(102, 126, 234, 0.2);
				transform: translateY(-2rpx);
			}

			// 确保隐藏时不占用空间但保持布局稳定
			&[style*="display: none"] {
				display: none !important;
			}
		}
	}

	.overview-cards {
		.overview-item {
			text-align: center;
			flex: 1;
		}
	}

	.tabs-container {
		position: sticky;
		top: 140rpx; // 调整位置，在主tab和时间选择器之后
		z-index: 10;
	}

	.expense-type-tabs {
		.expense-type-item {
			flex: 1;
			text-align: center;
			padding: 16rpx 24rpx;
			border-radius: 8rpx;
			margin: 0 4rpx;
			transition: all 0.3s ease;
			color: #666;
			font-size: 28rpx;

			&.active {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: white;
				font-weight: bold;
				transform: scale(1.02);
			}

			&:hover {
				background: rgba(102, 126, 234, 0.1);
			}
		}
	}

	.charts-container {
		padding-bottom: 40rpx;
	}

	.chart-header {
		margin-bottom: 20rpx;
		border-bottom: 1px solid #f0f0f0;
		padding-bottom: 16rpx;

		.yearly-stats {
			text-align: right;

			.stat-item {
				.stat-label {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 4rpx;
				}

				.stat-value {
					font-size: 28rpx;
					font-weight: bold;
				}
			}
		}
	}

	.charts-box {
		width: 100%;
		min-height: 300px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100%;
		color: #999;

		.empty-icon {
			font-size: 48rpx;
			margin-bottom: 16rpx;
			opacity: 0.6;
		}

		.empty-text {
			font-size: 28rpx;
			color: #999;
		}
	}

	// 响应式设计
	@media (max-width: 750rpx) {
		.overview-cards {
			.flex {
				flex-direction: column;
				gap: 16rpx;
			}

			.overview-item {
				padding: 16rpx;
				background: rgba(255, 255, 255, 0.8);
				border-radius: 8rpx;
			}
		}
	}
</style>
