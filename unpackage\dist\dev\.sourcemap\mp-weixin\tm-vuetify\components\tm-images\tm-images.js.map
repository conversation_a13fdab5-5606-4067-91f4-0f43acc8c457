{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-images/tm-images.vue?b54f", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-images/tm-images.vue?9aba", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-images/tm-images.vue?5c94", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-images/tm-images.vue?1668", "uni-app:///tm-vuetify/components/tm-images/tm-images.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-images/tm-images.vue?6aec", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-images/tm-images.vue?d6a8"], "names": ["name", "props", "src", "type", "default", "width", "height", "previmage", "model", "round", "data", "w", "h", "isLoad", "isError", "computed", "w_px", "h_px", "src_path", "mounted", "methods", "error", "loadPic", "wh", "click", "uni", "current", "urls", "fail"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0BjrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,eAaA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA,IACA,sCACA,sCACA,uCACA,qCACA,qCACA;QACA;MACA;MACA;IAEA;EACA;EACAC;IACA;EAEA;EACAC;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACA;gBACA;gBACA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACA;4BACA;4BACA;4BACA;4BACA;8BAEA;8BACA;8BACA;gCACAlB;gCACAC;8BACA;8BACA;4BACA;4BACA;8BACA;8BACA;8BACA;gCACAD;gCACAC;8BACA;8BACA;4BACA;4BACA;8BACA;8BACA;8BACA;gCACAD;gCACAC;8BACA;8BACA;4BACA;4BACA;8BACA;8BACA;8BACA;gCACAD;gCACAC;8BACA;8BAEA;4BACA;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAkB;MACA;MACA;QACAC;UACAC;UACAC;UACAC,0BAEA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChLA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-images/tm-images.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-images.vue?vue&type=template&id=5274b527&scoped=true&\"\nvar renderjs\nimport script from \"./tm-images.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-images.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-images.vue?vue&type=style&index=0&id=5274b527&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5274b527\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-images/tm-images.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-images.vue?vue&type=template&id=5274b527&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-images.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-images.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view @click=\"click\" class=\"tm-images overflow fulled \" :class=\"[round=='rounded'?'rounded':`round-${round}`]\">\r\n\t\t<view class=\"fulled fulled-height tm-images-load flex-center\">\r\n\t\t\t<view class=\"d-inline-block load\">\r\n\t\t\t\t<text v-if=\"isLoad\" class=\"iconfont icon-loading text-size-n text-grey\"></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"d-inline-block\" v-if=\"isError\">\r\n\t\t\t\t<slot name=\"error\">\r\n\t\t\t\t\t<text class=\"iconfont icon-exclamationcircle-f text-size-xl text-grey-lighten-2\"></text>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<image v-show=\"!isLoad\"  @error=\"error\"  \r\n\t\t\t:class=\"[round=='rounded'?'rounded':`round-${round}`]\" \r\n\t\t\t:style=\"{\r\n\t\t\t\twidth:w+'px',\r\n\t\t\t\theight:h+'px'\r\n\t\t\t}\" \r\n\t\t\t@load=\"loadPic\" :src=\"src_path\" :mode=\"model\"></image>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * 图片\r\n\t * @property {Function} load 加载成功时触发返回图片宽高。\r\n\t * @property {Function} click 点击图片事件，返回图片地址参数。\r\n\t * @property {Function} error 图片加载出错时触发。\r\n\t * @property {String} src = [] 默认：\"\",必填。图片地址。测试图片：https://picsum.photos/300\r\n\t * @property {Number} width = [] 默认：0,宽度，非必填,单位rpx\r\n\t * @property {Number} height = [] 默认：0,高度，非必填,单位rpx\r\n\t * @property {Number} round = [] 默认：0,圆角，非必填\r\n\t * @property {Boolean|String} previmage = [true|false] 默认：true,点击图片是否预览。\r\n\t * @property {String} model = [scaleToFill|aspectFit|aspectFill|widthFix|heightFix|top|bottom|center|left|right|top left|top right|bottom left|bottom right] 默认：scaleToFill,图片展现模式，同官方。\r\n\t * @example <tm-images src=\"https://picsum.photos/300\"></tm-images>\r\n\t */\r\n\texport default {\r\n\t\tname: \"tm-images\",\r\n\t\tprops: {\r\n\t\t\tsrc: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\t//自动，宽度撑满容器宽度，高度自动。\r\n\t\t\t// 自定宽度，\r\n\t\t\twidth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 自定高度。\r\n\t\t\theight: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 是否开启预览模式，即点击图片可以预览。\r\n\t\t\tprevimage: {\r\n\t\t\t\ttype: Boolean | String,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tmodel: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'scaleToFill'\r\n\t\t\t},\r\n\t\t\tround: {\r\n\t\t\t\ttype: Number|String,\r\n\t\t\t\tdefault: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tw: 0,\r\n\t\t\t\th: 0,\r\n\t\t\t\tisLoad:false,\r\n\t\t\t\tisError:false\r\n\t\t\t\t\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed:{\r\n\t\t\tw_px:function(){\r\n\t\t\t\treturn uni.upx2px(this.width);\r\n\t\t\t},\r\n\t\t\th_px:function(){\r\n\t\t\t\treturn uni.upx2px(this.height);\r\n\t\t\t},\r\n\t\t\tsrc_path:function(){\r\n\t\t\t\tif(\r\n\t\t\t\tthis.src.substring(0,4)=='http'||\r\n\t\t\t\tthis.src.substring(0,4)=='blob'||\r\n\t\t\t\tthis.src.substring(0,5)=='https'||\r\n\t\t\t\tthis.src.substring(0,3)=='ftp'||\r\n\t\t\t\tthis.src.indexOf('data:image')>-1\r\n\t\t\t\t){\r\n\t\t\t\t\treturn this.src;\r\n\t\t\t\t}\r\n\t\t\t\treturn '/'+this.src;\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.isLoad = true;\r\n\t\t\t\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\terror(e) {\r\n\t\t\t\tthis.isLoad = false;\r\n\t\t\t\tthis.isError = true;\r\n\t\t\t\tthis.$emit('error', e);\r\n\t\t\t},\r\n\t\t\tasync loadPic(e) {\r\n\t\t\t\tlet wh = e.detail;\r\n\t\t\t\tthis.isLoad = false;\r\n\t\t\t\tthis.isError = false;\r\n\t\t\t\tthis.$nextTick(async function(){\r\n\t\t\t\t\tthis.$Querey(\".tm-images\",this,30).then(tb=>{\r\n\t\t\t\t\t\tlet sw = tb[0].width||wh.width;\r\n\t\t\t\t\t\tlet sh = tb[0].height||wh.height;\r\n\t\t\t\t\t\tlet bl = wh.width / wh.height;\r\n\t\t\t\t\t\tif (this.w_px == 0 && this.h_px == 0) {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tthis.w = sw;\r\n\t\t\t\t\t\t\tthis.h = sw / bl;\r\n\t\t\t\t\t\t\tthis.$emit('load', {\r\n\t\t\t\t\t\t\t\twidth: this.w,\r\n\t\t\t\t\t\t\t\theight: this.h\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.w_px == 0 && this.h_px > 0) {\r\n\t\t\t\t\t\t\tthis.w = this.h_px * bl;\r\n\t\t\t\t\t\t\tthis.h = this.h_px\r\n\t\t\t\t\t\t\tthis.$emit('load', {\r\n\t\t\t\t\t\t\t\twidth: this.w,\r\n\t\t\t\t\t\t\t\theight: this.h\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.w_px > 0 && this.h_px == 0) {\r\n\t\t\t\t\t\t\tthis.w = this.w_px;\r\n\t\t\t\t\t\t\tthis.h = this.w_px / bl\r\n\t\t\t\t\t\t\tthis.$emit('load', {\r\n\t\t\t\t\t\t\t\twidth: this.w,\r\n\t\t\t\t\t\t\t\theight: this.h\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.w_px > 0 && this.h_px > 0) {\r\n\t\t\t\t\t\t\tthis.w = this.w_px;\r\n\t\t\t\t\t\t\tthis.h = this.h_px;\r\n\t\t\t\t\t\t\tthis.$emit('load', {\r\n\t\t\t\t\t\t\t\twidth: this.w,\r\n\t\t\t\t\t\t\t\theight: this.h\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tclick(e) {\r\n\t\t\t\tthis.$emit(\"click\", this.src_path);\r\n\t\t\t\tif (this.previmage&&!this.isError) {\r\n\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\tcurrent: this.src_path,\r\n\t\t\t\t\t\turls: [this.src_path],\r\n\t\t\t\t\t\tfail:(res)=>{\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tm-images{\r\n\tline-height: 0;\r\n\t\r\n\t.tm-images-load{\r\n\t\tmin-width: 60rpx;\r\n\t\tmin-height: 60rpx;\r\n\t\t.load{\r\n\t\t\tanimation: xhRote 0.8s infinite linear;\r\n\t\t}\r\n\t}\r\n}\r\n@keyframes xhRote{\r\n\t0%{\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\t\r\n\t100%{\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n</style>\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-images.vue?vue&type=style&index=0&id=5274b527&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-images.vue?vue&type=style&index=0&id=5274b527&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775124\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}