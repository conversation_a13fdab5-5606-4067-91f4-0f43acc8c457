{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-slider/tm-slider.vue?ecb0", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-slider/tm-slider.vue?1cf1", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-slider/tm-slider.vue?35c9", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-slider/tm-slider.vue?12d7", "uni-app:///tm-vuetify/components/tm-slider/tm-slider.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-slider/tm-slider.vue?19cc", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-slider/tm-slider.vue?5428"], "names": ["name", "components", "tmIcons", "model", "prop", "event", "props", "type", "default", "vertical", "height", "width", "showLeft", "showRight", "max", "value", "valueDiog", "disabled", "step", "color", "bgColor", "leftIcon", "rightIcon", "showTip", "fllowTheme", "black", "watch", "barLeft", "ruslt", "computed", "black_tmeme", "kdcNum", "ar", "index", "left", "kedu", "active_width", "heightpx", "color_tmeme", "data", "slider<PERSON><PERSON><PERSON>", "x", "startMove", "isError", "showTopTips", "mounted", "Math", "rdl", "updated", "methods", "barStart", "barEnd", "bar<PERSON>ove", "getwidth", "res"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eC4EjrB;EACAA;EACAC;IAAAC;EAAA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;IACAN;MACAO;MACAC;IACA;IACAC;IAAA;IACA;IACAC;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;IAAA;IACAC;IAAA;IACA;IACAC;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;IACA;IACAC;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACAiB;MACAlB;MACAC;IACA;EACA;EACAkB;IACAX;MACA;MACA;IAEA;IACAY;MACA;MAEA;QACA;UACA;UAEA;UAEA;UACA;YACAC;UACA;YACAA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;UACAC;UAAA;UACAC;UAAA;UACAC;QACA;MACA;;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAf;MACAgB;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;gBAAA;gBAAA;kBAAA;oBAAA;sBAAA;wBAAA;wBAAA,OACA;sBAAA;wBAAA,MACAC;0BAAA;0BAAA;wBAAA;wBACA;wBAAA;sBAAA;wBAGAC;wBACA;sBAAA;sBAAA;wBAAA;oBAAA;kBAAA;gBAAA;cAAA,CACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;gBAAA;gBAAA;kBAAA;oBAAA;sBAAA;wBAAA;wBAAA,OACA;sBAAA;wBAAA,MACAF;0BAAA;0BAAA;wBAAA;wBACA;wBAAA;sBAAA;wBAGAC;wBACA;sBAAA;sBAAA;wBAAA;oBAAA;kBAAA;gBAAA;cAAA,CACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAE;IACAC;MAEA;MACA;QACA;QACA;QACA;MACA;QACA;UACA;UACA;UACA;QACA;MACA;MAEA;QACA;MACA;IACA;IACAC;MAEA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAlB;MACA;QACAA;MACA;MACA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;MACA;MAEA;QACA;;QAEA;UACA;UACA;QACA;UACA;QACA;MACA;IAEA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACAA;gBACAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAEA,0GACA;gBAAA;cAAA;gBAGA;kBACA,0GACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClVA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-slider/tm-slider.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-slider.vue?vue&type=template&id=00b559b9&scoped=true&\"\nvar renderjs\nimport script from \"./tm-slider.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-slider.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-slider.vue?vue&type=style&index=0&id=00b559b9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"00b559b9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-slider/tm-slider.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-slider.vue?vue&type=template&id=00b559b9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  _vm.$initSSP()\n  if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n    _vm.$setSSP(\"left\", {\n      data: {\n        value: _vm.value,\n        color: _vm.color_tmeme,\n        icon: _vm.leftIcon,\n        max: _vm.max,\n      },\n    })\n    _vm.$setSSP(\"tips\", {\n      data: _vm.value,\n    })\n    _vm.$setSSP(\"right\", {\n      data: {\n        value: _vm.value,\n        color: _vm.color_tmeme,\n        icon: _vm.rightIcon,\n        max: _vm.max,\n      },\n    })\n  }\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-slider.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-slider.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view  class=\"fulled tm-slider \" :class=\"[step > 0 ? 'pb-36' : 'pb-24']\"\r\n\t\t:style=\"{ height: vertical ? heightpx + 'px' : 'auto', width: vertical ? '5px' : '100%'}\">\r\n\t\t<view class=\" tm-slider-id fulled \" :class=\"[vertical ? 'vertical' : 'flex-between']\">\r\n\t\t\t<!-- 左边label -->\r\n\t\t\t<view v-if=\"showLeft\" class=\"label_slider left  flex-col text-size-xs text-grey-darken-1\"\r\n\t\t\t\t:class=\"[vertical ? '' : 'flex-center']\">\r\n\t\t\t\t<slot name=\"left\" :data=\"{ value: value, color: color_tmeme, icon: leftIcon,max:max }\">\r\n\t\t\t\t\t<tm-icons v-if=\"leftIcon && !vertical\" size=\"28\" :name=\"leftIcon\" :color=\"color_tmeme\"></tm-icons>\r\n\t\t\t\t\t<view class=\"text-size-xs text-grey-darken-1 \">{{ value }}</view>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<!-- 条子内容 -->\r\n\t\t\t<view class=\"slider_id \" \r\n\t\t\t\t:style=\"{ width: vertical ? 'auto' : sliderWidth + 'px', height: vertical ? sliderWidth + 'px' : '5px' }\">\r\n\t\t\t\t<view class=\"slider_id_bg  round-10\" :class=\"[bgColor,black_tmeme?'bk':'']\"></view>\r\n\t\t\t\t<view\r\n\t\t\t\t\t:style=\"{ width: vertical ? '100%' : active_width + '%', height: vertical ? active_width + '%' : '100%' }\"\r\n\t\t\t\t\tclass=\"slider_id_active round-10 \" :class=\"[color_tmeme,black_tmeme?'bk':'']\"></view>\r\n\t\t\t\t<view  :style=\"{ left: vertical ? 0 : barLeft + 'px', top: vertical ? barLeft + 'px' : 0 }\"\r\n\t\t\t\t\t@touchcancel=\"barEnd\" @touchstart=\"barStart\" @touchend=\"barEnd\" @touchmove.stop.prevent=\"barMove\"\r\n\t\t\t\t\t@mouseleave=\"barEnd\" @mousedown=\"barStart\" @mouseup=\"barEnd\" @mousemove.stop.prevent=\"barMove\"\r\n\t\t\t\t\tclass=\"slider_bar border-white-a-2  rounded\" :class=\"[color_tmeme,black_tmeme?'bk':'', ` shadow-${color_tmeme}-10`]\">\r\n\t\t\t\t\t<view v-if=\"showTopTips||showTip\" class=\"slider_bar_showbg border-white-a-1\" :class=\"[color_tmeme,black_tmeme?'bk':'']\"></view>\r\n\t\t\t\t\t<view v-if=\"showTopTips||showTip\" class=\"slider_bar_num text-size-xs flex-center\" :class=\"[color_tmeme,black_tmeme?'bk':'']\">\r\n\t\t\t\t\t\t<slot name=\"tips\" :data=\"value\">{{ value }}</slot>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 步长刻度尺 -->\r\n\t\t\t\t<view class=\"kdc_wk \">\r\n\t\t\t\t\t<view v-for=\"(item, index) in kdcNum\" :key=\"index\"\r\n\t\t\t\t\t\t:style=\"{ left: vertical ? 0 : item.left, top: vertical ? item.left : 0 }\" :class=\"[color_tmeme,black_tmeme?'bk':'']\"\r\n\t\t\t\t\t\tclass=\"kdc_wk_item rounded  border-white-a-1 \">\r\n\t\t\t\t\t\t<view class=\"kdc_wk_item_label  pl-0\" :class=\"[vertical ? 'pl-24' : 'pa-24']\">\r\n\t\t\t\t\t\t\t<text class=\"text-size-xs text-grey-darken-1 \">{{ item.kedu }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 右边label -->\r\n\t\t\t<view v-if=\"showRight\" class=\"label_slider right  flex-col text-size-xs text-grey-darken-1\"\r\n\t\t\t\t:class=\"[vertical ? 'flex-end' : 'flex-center']\">\r\n\t\t\t\t<slot name=\"right\" :data=\"{ value: value, color: color_tmeme, icon: rightIcon,max:max }\">\r\n\t\t\t\t\t<tm-icons v-if=\"rightIcon && !vertical\" size=\"28\" :name=\"rightIcon\" :color=\"color_tmeme\"></tm-icons>\r\n\t\t\t\t\t<view class=\"text-size-xs text-grey-darken-1 \">{{ max }}</view>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\r\n\t/**\r\n\t * 滑块\r\n\t * @property {Number} value = [0] 赋值，如果需要同步使用value.sync推荐使用v-model绑定。\r\n\t * @property {Boolean} vertical = [true|false] 默认：false,是否启用竖向模式。\r\n\t * @property {Number} height = [200] 默认：200,竖向模式时才有作用。\r\n\t * @property {Number} width = [] 默认：0,横向时起作用，如果为0自动获取外层宽度。\r\n\t * @property {Boolean} show-left = [true|false] 默认：false,显示左边数据。\r\n\t * @property {Boolean} show-right = [true|false] 默认：false,显示右边数据。\r\n\t * @property {Number} max = [] 默认：100,显示的最大刻度。\r\n\t * @property {Number} value-diog = [] 默认：0,取值小数点后几位\r\n\t * @property {Boolean} disabled = [true|false] 默认：false, 是否禁用。\r\n\t * @property {Boolean} showTip = [true|false] 默认：false, 始终显示进度标签。\r\n\t * @property {Number} step = [10|20] 默认：0, 步长,设置请尽量大于20.太小滑动容易过小出问题。\r\n\t * @property {String} color = [primary] 默认：primary,  主题颜色名称。\r\n\t * @property {String} bg-color = [grey-lighten-2] 默认：grey-lighten-2,  底部不活动的背景色,颜色名称。\r\n\t * @property {String} left-icon = [] 默认：icon-minus,   左边图标\r\n\t * @property {String} right-icon = [] 默认：icon-plus,   右边图标\r\n\t * @property {String} name = [] 默认：''，提交表单时的的字段名称标识\r\n\t * @property {Function} change 同v-model和value相等的参数，变动时触发。\r\n\t * @example <tm-slider v-model=\"checked\"  ></tm-slider>\r\n\t * \r\n\t */\r\n\timport tmIcons from \"@/tm-vuetify/components/tm-icons/tm-icons.vue\"\r\n\texport default {\r\n\t\tname:'tm-slider',\r\n\t\tcomponents:{tmIcons},\r\n\t\tmodel:{\r\n\t\t\tprop:'value',\r\n\t\t\tevent:'input'\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t//提交表单时的的字段名称\r\n\t\t\tname:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:''\r\n\t\t\t},\r\n\t\t\tvertical: Boolean, //是否启用竖向模式。需要和height配合使用。\r\n\t\t\t// 单位upx.\r\n\t\t\theight: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 200\r\n\t\t\t},\r\n\t\t\twidth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tshowLeft: Boolean, //显示左边\r\n\t\t\tshowRight: Boolean, //显示右边\r\n\t\t\t// 最大刻度。\r\n\t\t\tmax: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 100\r\n\t\t\t},\r\n\t\t\t// 默认的数据。不能大于max.,使用.sync修饰。双向绑定数据。\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 取值小数点后几位。默认为0\r\n\t\t\tvalueDiog: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 是否禁用。\r\n\t\t\tdisabled: Boolean,\r\n\t\t\t//步长。默认为0,设置请尽量大于0.太小滑动容易过小出问题。\r\n\t\t\tstep: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 主题颜色名称。\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'primary'\r\n\t\t\t},\r\n\t\t\t// 底部不活动的背景色,颜色名称。。\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'grey-lighten-2'\r\n\t\t\t},\r\n\t\t\t// 左边图标名称。\r\n\t\t\tleftIcon: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: 'icon-minus'\r\n\t\t\t},\r\n\t\t\t// 右边图标名称。\r\n\t\t\trightIcon: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: 'icon-plus'\r\n\t\t\t},\r\n\t\t\t// 始终显示进度提示窗\r\n\t\t\tshowTip: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 跟随主题色的改变而改变。\r\n\t\t\tfllowTheme:{\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:true\r\n\t\t\t},\r\n\t\t\tblack: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tvalue: function(val) {\r\n\t\t\t\tlet rdl = this.sliderWidth * (Math.abs(val) / Math.abs(this.max));\r\n\t\t\t\tthis.barLeft = rdl >= this.sliderWidth || rdl < 0 ? this.sliderWidth : rdl;\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tbarLeft: function() {\r\n\t\t\t\tlet value = (this.barLeft / this.sliderWidth) * this.max;\r\n\t\t\t\t\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tif (this.valueDiog > 0) {\r\n\t\t\t\t\t\tlet s = value.toString();\r\n\r\n\t\t\t\t\t\tlet st = s.split('.');\r\n\r\n\t\t\t\t\t\tlet ruslt = 0;\r\n\t\t\t\t\t\tif (st.length > 1) {\r\n\t\t\t\t\t\t\truslt = parseFloat(st[0] + '.' + st[1].substring(0, this.valueDiog));\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\truslt = parseFloat(s);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$emit('update:value', ruslt);\r\n\t\t\t\t\t\tthis.$emit('input', ruslt);\r\n\t\t\t\t\t\tthis.$emit('change', ruslt);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$emit('update:value', parseInt(value));\r\n\t\t\t\t\t\tthis.$emit('input', parseInt(value));\r\n\t\t\t\t\t\tthis.$emit('change', parseInt(value));\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tblack_tmeme: function() {\r\n\t\t\t\tif (this.black !== null) return this.black;\r\n\t\t\t\treturn this.$tm.vx.state().tmVuetify.black;\r\n\t\t\t},\r\n\t\t\t// 计算刻度尺的数量\r\n\t\t\tkdcNum: function() {\r\n\t\t\t\tif (Math.abs(this.step) <= 0) return [];\r\n\t\t\t\tlet jlv = Math.abs(this.step) / Math.abs(this.max);\r\n\t\t\t\tlet left_width = jlv * (this.sliderWidth - 0);\r\n\t\t\t\tlet kd_num = parseInt(this.sliderWidth / left_width);\r\n\t\t\t\tlet ar = [];\r\n\t\t\t\tfor (let i = 0; i <= kd_num; i++) {\r\n\t\t\t\t\tar.push({\r\n\t\t\t\t\t\tindex: i, //顺序\r\n\t\t\t\t\t\tleft: i * left_width + 'px', //距离左边距离\r\n\t\t\t\t\t\tkedu: this.step * i //当前刻度数量。\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\treturn ar;\r\n\t\t\t},\r\n\t\t\t//比例\r\n\t\t\tactive_width: function() {\r\n\t\t\t\treturn (this.barLeft / this.sliderWidth) * 100;\r\n\t\t\t},\r\n\t\t\theightpx: function() {\r\n\t\t\t\treturn uni.upx2px(this.height);\r\n\t\t\t},\r\n\t\t\tcolor_tmeme:function(){\r\n\t\t\t\tif(this.$tm.vx.state().tmVuetify.color!==null&&this.$tm.vx.state().tmVuetify.color && this.fllowTheme){\r\n\t\t\t\t\treturn this.$tm.vx.state().tmVuetify.color;\r\n\t\t\t\t}\r\n\t\t\t\treturn this.color;\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsliderWidth: 0,\r\n\t\t\t\tx: 0,\r\n\t\t\t\tstartMove: false,\r\n\t\t\t\tbarLeft: 0,\r\n\t\t\t\tisError: false,\r\n\t\t\t\tshowTopTips: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\tthis.$nextTick(async function() {\r\n\t\t\t\tawait this.getwidth();\r\n\t\t\t\tif (Math.abs(this.value) > Math.abs(this.max)) {\r\n\t\t\t\t\tthis.isError = true;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet rdl = this.sliderWidth * (Math.abs(this.value) / Math.abs(this.max));\r\n\t\t\t\tthis.barLeft = rdl >= this.sliderWidth || rdl < 0 ? this.sliderWidth : rdl;\r\n\t\t\t});\r\n\t\t},\r\n\t\tasync updated() {\r\n\t\t\tthis.$nextTick(async function() {\r\n\t\t\t\tawait this.getwidth();\r\n\t\t\t\tif (Math.abs(this.value) > Math.abs(this.max)) {\r\n\t\t\t\t\tthis.isError = true;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet rdl = this.sliderWidth * (Math.abs(this.value) / Math.abs(this.max));\r\n\t\t\t\tthis.barLeft = rdl >= this.sliderWidth || rdl < 0 ? this.sliderWidth : rdl;\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tbarStart(e) {\r\n\t\t\t\t\r\n\t\t\t\tif (this.disabled || this.isError) return;\r\n\t\t\t\tif(e.type.indexOf('mouse')>-1&&e.changedTouches.length==0){\r\n\t\t\t\t\tthis.x = (this.vertical ? e.pageY : e.pageX) - this.barLeft;\r\n\t\t\t\t\tthis.startMove = true;\r\n\t\t\t\t\tthis.showTopTips = true;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif (e.changedTouches.length > 0) {\r\n\t\t\t\t\t\tthis.x = (this.vertical ? e.changedTouches[0].pageY : e.changedTouches[0].pageX) - this.barLeft;\r\n\t\t\t\t\t\tthis.startMove = true;\r\n\t\t\t\t\t\tthis.showTopTips = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.$nextTick(function(){\r\n\t\t\t\t\tthis.$emit('start',this.value)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tbarEnd(e) {\r\n\t\t\t\t\r\n\t\t\t\tif (this.disabled || this.isError) return;\r\n\t\t\t\tthis.startMove = false;\r\n\t\t\t\tthis.showTopTips = false;\r\n\t\t\t\tthis.$nextTick(function(){\r\n\t\t\t\t\tthis.$emit('end',this.value)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tbarMove(e) {\r\n\t\t\t\tif (this.disabled || this.isError) return;\r\n\t\t\t\tif (!this.startMove) return;\r\n\t\t\t\tlet left = 0;\r\n\t\t\t\tif(e.type.indexOf('mouse')>-1&&e.changedTouches.length==0){\r\n\t\t\t\t\tleft = (this.vertical ? e.pageY : e.pageX) - this.x;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tleft = (this.vertical ? e.changedTouches[0].pageY : e.changedTouches[0].pageX) - this.x;\r\n\t\t\t\t}\r\n\t\t\t\tif (left <= 0) {\r\n\t\t\t\t\tthis.barLeft = 0;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (left >= this.sliderWidth) {\r\n\t\t\t\t\tthis.barLeft = this.sliderWidth;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet nowStep = parseInt(Math.abs(left - this.barLeft));\r\n\t\t\t\tlet bdi = parseInt((this.step / this.max) * this.sliderWidth);\r\n\t\t\t\t\r\n\t\t\t\tif (nowStep >= bdi) {\r\n\t\t\t\t\t// 每一小段的值。\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(this.step!==0){\r\n\t\t\t\t\t\tlet kedud = this.sliderWidth / (this.max / this.step);\r\n\t\t\t\t\t\tthis.barLeft = Math.round(left / kedud) * kedud;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.barLeft = left;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tasync getwidth() {\r\n\t\t\t\tlet res = await this.$Querey('.tm-slider-id', this,0).catch(e=>{});\r\n\t\t\t\tres[0].width = res[0].width||uni.upx2px(this.width);\r\n\t\t\t\tres[0].height = res[0].height||uni.upx2px(this.height);\r\n\t\t\t\tif (this.showLeft === false && this.showRight === false) {\r\n\t\t\t\t\tthis.sliderWidth = this.vertical ? res[0].height : res[0].width;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.showLeft !== false && this.showRight !== false) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.sliderWidth = (this.vertical ? res[0].height : res[0].width) - uni.upx2px(this.vertical ? 50 :\r\n\t\t\t\t\t\t100) * 2;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.showLeft === true || this.showRight === true) {\r\n\t\t\t\t\tthis.sliderWidth = (this.vertical ? res[0].height : res[0].width) - uni.upx2px(this.vertical ? 50 :\r\n\t\t\t\t\t\t100);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.label_slider {\r\n\t\tflex-shrink: 0;\r\n\r\n\t\t&.left,\r\n\t\t&.right {\r\n\t\t\twidth: 100upx;\r\n\t\t}\r\n\t}\r\n\r\n\t.tm-slider-id {\r\n\t\twidth: 100%;\r\n\r\n\t\t.slider_id {\r\n\t\t\tposition: relative;\r\n\t\t\theight: 4px;\r\n\r\n\t\t\t.slider_id_bg {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.slider_id_active {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tz-index: 5;\r\n\t\t\t}\r\n\r\n\t\t\t.slider_bar {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\twidth: 40upx;\r\n\t\t\t\theight: 40upx;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tmargin-top: -22upx;\r\n\t\t\t\tz-index: 10;\r\n\r\n\t\t\t\t.slider_bar_showbg {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\twidth: 50upx;\r\n\t\t\t\t\theight: 50upx;\r\n\t\t\t\t\tborder-radius: 30upx;\r\n\t\t\t\t\tborder-bottom-left-radius: 5upx;\r\n\t\t\t\t\ttransform: rotate(-45deg);\r\n\t\t\t\t\tbottom: 60upx;\r\n\t\t\t\t\tleft: -9upx;\r\n\t\t\t\t\tanimation: roteScaleTop_BG 0.3s ease-in-out;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.slider_bar_num {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\twidth: 50upx;\r\n\t\t\t\t\theight: 50upx;\r\n\t\t\t\t\tborder-radius: 50upx;\r\n\t\t\t\t\tleft: -8upx;\r\n\t\t\t\t\tbottom: 60upx;\r\n\t\t\t\t\tline-height: 50upx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tbackground: transparent !important;\r\n\t\t\t\t\tanimation: roteScaleTop 0.3s ease-in-out;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.kdc_wk {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 6;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\ttop: -2upx;\r\n\r\n\t\t\t\t.kdc_wk_item {\r\n\t\t\t\t\twidth: 10upx;\r\n\t\t\t\t\theight: 10upx;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tz-index: 7;\r\n\t\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t\t.kdc_wk_item_label {\r\n\t\t\t\t\t\tmargin-left: -100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.vertical {\r\n\t\t\theight: 100%;\r\n\r\n\t\t\t.label_slider {\r\n\t\t\t\tflex-shrink: 0;\r\n\r\n\t\t\t\t&.left,\r\n\t\t\t\t&.right {\r\n\t\t\t\t\twidth: auto;\r\n\t\t\t\t\theight: 50upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.slider_bar {\r\n\t\t\t\tmargin-left: -18upx;\r\n\t\t\t}\r\n\r\n\t\t\t.kdc_wk {\r\n\t\t\t\ttop: 0upx;\r\n\r\n\t\t\t\t.kdc_wk_item {\r\n\t\t\t\t\t.kdc_wk_item_label {\r\n\t\t\t\t\t\tmargin-left: 100%;\r\n\t\t\t\t\t\tmargin-top: -15upx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t@keyframes roteScaleTop_BG{\r\n\t\t0%{\r\n\t\t\ttransform: scale(0.9) translateY(20rpx) rotate(-45deg);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\t\t100%{\r\n\t\t\ttransform: scale(1)  translateY(0rpx) rotate(-45deg);\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\t@keyframes roteScaleTop{\r\n\t\t0%{\r\n\t\t\ttransform: scale(0.9) translateY(20rpx);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\t\t100%{\r\n\t\t\ttransform: scale(1)  translateY(0rpx);\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-slider.vue?vue&type=style&index=0&id=00b559b9&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-slider.vue?vue&type=style&index=0&id=00b559b9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775142\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}