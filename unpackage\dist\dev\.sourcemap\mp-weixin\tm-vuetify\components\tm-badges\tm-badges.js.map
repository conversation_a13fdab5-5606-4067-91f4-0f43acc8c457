{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-badges/tm-badges.vue?cc94", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-badges/tm-badges.vue?ed5d", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-badges/tm-badges.vue?869d", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-badges/tm-badges.vue?8127", "uni-app:///tm-vuetify/components/tm-badges/tm-badges.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-badges/tm-badges.vue?16c9", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-badges/tm-badges.vue?5a74"], "names": ["components", "tmIcons", "name", "props", "label", "type", "default", "dot", "icon", "iconSize", "iconWidth", "color", "iconColor", "offset", "fllowTheme", "computed", "offses", "transform", "color_tmeme", "data", "methods", "onclick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;gBCoCjrB;EACAA;IAAAC;EAAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IACAC;MACA;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC3GA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-badges/tm-badges.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-badges.vue?vue&type=template&id=3d2d2497&scoped=true&\"\nvar renderjs\nimport script from \"./tm-badges.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-badges.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-badges.vue?vue&type=style&index=0&id=3d2d2497&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3d2d2497\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-badges/tm-badges.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-badges.vue?vue&type=template&id=3d2d2497&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-badges.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-badges.vue?vue&type=script&lang=js&\"", "<!-- 徽章 -->\r\n<template>\r\n\t<view class=\"tm--badges  fulled\">\r\n\t\t<view @click.stop=\"onclick\" v-if=\"icon\" class=\"tm--badges--cm icons flex-center border-white-a-1\" :class=\"[color]\" :style=\"offses+`;width:${iconWidth}rpx;height:${iconWidth}rpx;`\">\r\n\t\t\t<view class=\"d-inline-block flex-center vertical-align-middle\" style=\"transform: scale(0.7); line-height: 0;\">\r\n\t\t\t\t<tm-icons style=\"line-height: 0;\" :size=\"iconSize\" :color=\"iconColor\" dense :name=\"icon\"></tm-icons>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t\t@click=\"onclick\"\r\n\t\t\tv-if=\"!icon\"\r\n\t\t\t:style=\"offses\"\r\n\t\t\tclass=\"tm--badges--cm d-inline-block px-6 \"\r\n\t\t\t:class=\"[color_tmeme, label != '' && !dot ? 'num shadow-red-10' : 'dot', 'flex-center']\"\r\n\t\t>\r\n\t\t\t<text v-if=\"!dot\" class=\"text-size-xs\">{{ label }}</text>\r\n\t\t</view>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * 徽章、角标\r\n * @property {String} label = [] 默认：''，当填入信息时即显示数字角标。\r\n * @property {Number} icon-size = [] 默认：24，当为图标时，可以设置图标大小。\r\n * @property {Number} icon-width = [] 默认：32，当为图标时，可以设置宽高。\r\n * @property {String} color = [] 默认：red，主题背景色\r\n * @property {String} icon-color = [] 默认：white，图标主题背景色\r\n * @property {Boolean|String} dot = [] 默认：true，使用点。优先级高于label数字展示。\r\n * @property {String} icon = [] 默认：''，使用图标作为显示角标。\r\n * @property {Array} offset = [] 默认：[0,0]，位置,x,y偏移量。\r\n * @property {Funciton} click 点击角标时触发。\r\n * @example <tm-badges  />\r\n */\r\nimport tmIcons from '@/tm-vuetify/components/tm-icons/tm-icons.vue';\r\nexport default {\r\n\tcomponents: { tmIcons },\r\n\tname: 'tm-badges',\r\n\tprops: {\r\n\t\tlabel: {\r\n\t\t\ttype: String | Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 使用点。优先级高于label数字展示。\r\n\t\tdot: {\r\n\t\t\ttype: Boolean | String,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 使用图标展示\r\n\t\ticon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\ticonSize: {\r\n\t\t\ttype: Number|String,\r\n\t\t\tdefault: 24\r\n\t\t},\r\n\t\ticonWidth:{\r\n\t\t\ttype: Number|String,\r\n\t\t\tdefault: 32\r\n\t\t},\r\n\t\t// 主题色名称\r\n\t\tcolor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'red'\r\n\t\t},\r\n\t\t// 图标主题色名称\r\n\t\ticonColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'white'\r\n\t\t},\r\n\t\t// 位置[0,0]\r\n\t\toffset: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: () => {\r\n\t\t\t\treturn [0, 0];\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 跟随主题色的改变而改变。\r\n\t\tfllowTheme: {\r\n\t\t\ttype: Boolean | String,\r\n\t\t\tdefault: false\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\toffses: function() {\r\n\t\t\tlet p = uni.$tm.objToString({\r\n\t\t\t\ttransform: `translateX(${this.offset[0]}px) translateY(${this.offset[1]}px)`\r\n\t\t\t});\r\n\t\t\treturn p;\r\n\t\t},\r\n\t\tcolor_tmeme: function() {\r\n\t\t\tif (this.$tm.vx.state().tmVuetify.color !== null && this.$tm.vx.state().tmVuetify.color && this.fllowTheme) {\r\n\t\t\t\treturn this.$tm.vx.state().tmVuetify.color;\r\n\t\t\t}\r\n\t\t\treturn this.color;\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {};\r\n\t},\r\n\tmethods: {\r\n\t\tonclick(e) {\r\n\t\t\tthis.$emit('click', e);\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tm--badges {\r\n\tposition: relative;\r\n\t// pointer-events: none;\r\n\tdisplay: block;\r\n\t.tm--badges--cm {\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\t&.num {\r\n\t\t\twidth: auto;\r\n\t\t\tmin-width: 26rpx;\r\n\t\t\theight: 35rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t\tfont-size: 22upx;\r\n\t\t\tline-height: 35upx;\r\n\t\t\ttext-align: center;\r\n\t\t\t\r\n\t\t}\r\n\r\n\t\t&.icons {\r\n\t\t\t@extend .num;\r\n\t\t}\r\n\r\n\t\t&.dot {\r\n\t\t\twidth: 16upx;\r\n\t\t\theight: 16upx;\r\n\t\t\tmin-width: 0 !important;\r\n\t\t\tcolor: #fff;\r\n\t\t\tpadding: 0;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t}\r\n}\r\n</style>\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-badges.vue?vue&type=style&index=0&id=3d2d2497&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-badges.vue?vue&type=style&index=0&id=3d2d2497&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775145\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}