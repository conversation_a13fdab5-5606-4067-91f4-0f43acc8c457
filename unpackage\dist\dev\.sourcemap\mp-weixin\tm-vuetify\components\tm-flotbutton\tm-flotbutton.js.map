{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-flotbutton/tm-flotbutton.vue?a224", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-flotbutton/tm-flotbutton.vue?c30b", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-flotbutton/tm-flotbutton.vue?09ac", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-flotbutton/tm-flotbutton.vue?58f4", "uni-app:///tm-vuetify/components/tm-flotbutton/tm-flotbutton.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-flotbutton/tm-flotbutton.vue?c1a7", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-flotbutton/tm-flotbutton.vue?7bf1"], "names": ["name", "components", "tmButton", "props", "actions", "type", "default", "openType", "actionsPos", "showActions", "clickActionsHiden", "size", "width", "height", "showText", "label", "icon", "bgcolor", "fontSize", "fontColor", "iconSize", "color", "absolute", "safe", "fixed", "position", "offset", "fllowTheme", "computed", "offsets", "get", "set", "color_tmeme", "pos", "data", "position_info", "show", "isRender", "thisPos", "mounted", "methods", "init", "t", "getsafeJl", "posfun", "console", "transform", "right", "top", "click", "itemChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,+pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;gBCiErrB;EACAA;EACAC;IAAAC;EAAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IAEA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;QACA;MACA;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;EACA;EACAsB;IACAC;MACAC;QACA;MACA;MACAC;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACAH;QAEA;MACA;MACAC;QACA;MACA;IAEA;EACA;EACAG;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAC;IACAC;MACA;MACA;QACAC;QACAA;UAAA9B;UAAAC;QAAA;QACA6B;MACA;IAQA;IACAC;MACA;MAEA;MAQA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UAEA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MAEA;MACA;QAEA;UACA;QACA;UACA;QACA;UAEA;UACA;UACA;QAEA;UACA;UAEA;UACA;UAEA;QACA;UAEA;UACA;UAEA;YACA;YACA;UACA;QAEA;UACA;UACA;UACA;UACA;UACA;YACA;YACAC;YACA;UAEA;YACA;YACA;YACA;cACA;cACA;YAEA;UAEA;YACA;YACA;YACA;cACA;cACA;gBACAC;gBACAC;gBACAC;cACA;cACA;YACA;UAEA;QACA;MACA;IAEA;IACAC;MACA;MACA;IAEA;IACAC;MAEA;MACA;MACA;IAEA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC/VA;AAAA;AAAA;AAAA;AAAgxC,CAAgB,ssCAAG,EAAC,C;;;;;;;;;;;ACApyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-flotbutton/tm-flotbutton.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-flotbutton.vue?vue&type=template&id=3fef0e31&scoped=true&\"\nvar renderjs\nimport script from \"./tm-flotbutton.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-flotbutton.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-flotbutton.vue?vue&type=style&index=0&id=3fef0e31&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3fef0e31\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-flotbutton/tm-flotbutton.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-flotbutton.vue?vue&type=template&id=3fef0e31&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = (_vm.show || _vm.showActions) && _vm.actions.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-flotbutton.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-flotbutton.vue?vue&type=script&lang=js&\"", "<!-- 悬浮按钮 -->\r\n<template>\r\n\t<view class=\"nobody fulled\"\r\n\t\t:class=\"[absolute?'fulled':'',fixed?'d-inline-block':'',absolute&&position=='bottom'?'flex-end-center height100':'']\">\r\n\t\t<view  \r\n\t\t\t:class=\"[absolute?'fulled':'',absolute?'relative':'',absolute&&position=='top'?'flex-center':'',absolute&&position=='bottom'?'flex-end-center height100':'',fixed?'d-inline-block':'']\">\r\n\r\n\t\t\t<view  class=\"flotbtnId\" :style=\"pos\" :class=\"[absolute?'absolute':'',fixed?'fixed':'']\">\r\n\r\n\t\t\t\t<slot name=\"default\">\r\n\t\t\t\t\t<tm-button\r\n\t\t\t\t\tv-if=\"isRender\"\r\n\t\t\t\t\t@contact=\"$emit('contact', $event)\"\r\n\t\t\t\t\t@error=\"$emit('error', $event)\"\r\n\t\t\t\t\t@getphonenumber=\"$emit('getphonenumber', $event)\"\r\n\t\t\t\t\t@getuserinfo=\"$emit('getuserinfo', $event)\"\r\n\t\t\t\t\t@launchapp=\"$emit('launchapp', $event)\"\r\n\t\t\t\t\t@opensetting=\"$emit('opensetting', $event)\"\r\n\t\t\t\t\t@longpress=\"$emit('longpress', $event)\"\r\n\t\t\t\t\t@touchcancel=\"$emit('touchcancel', $event)\"\r\n\t\t\t\t\t:open-type=\"openType\"\r\n\t\t\t\t\t@click=\"click\"\r\n\t\t\t\t\t:padding=\"[0,0]\"\r\n\t\t\t\t\t :showValue=\"showText\" vertical :label=\"label\" :fontSize=\"fontSize\"\r\n\t\t\t\t\t\t :iconSize=\"iconSize\" :theme=\"color_tmeme\" round=\"rouned\" :font-color=\"fontColor\"  :bgcolor=\"bgcolor\"\r\n\t\t\t\t\t\t:size=\"size\" :width=\"width\" :height=\"width\" :icon=\"icon\" fab>\r\n\t\t\t\t\t</tm-button>\r\n\t\t\t\t</slot>\r\n\t\t\t\t<view  v-if=\"(show || showActions)&&actions.length>0\" class=\"menulistAction\" :class=\"[actionsPos]\">\r\n\t\t\t\t\t<view class=\"menulistAction_item\" v-for=\"(item,index) in actions\" :key=\"index\">\r\n\t\t\t\t\t\t<tm-button :fllowTheme=\"false\" fab @click=\"itemChange(index)\" iconSize=\"40\" :icon=\"item.icon\" :theme=\"item.color\" size=\"m\"></tm-button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * 悬浮按钮\r\n\t * @property {Array} actions = [] 默认：[],悬浮按钮展开的子按钮。格式[{icon,color}]\r\n\t * @property {String} actions-pos = [top | left | bottom | right] 默认：top,子菜单按钮显示的方向\r\n\t * @property {String|Boolean} show-actions = [false|true] 默认：false,始终展开子菜单。点击子菜单后不消失.\r\n\t * @property {String|Boolean} click-actions-hiden = [false|true] 默认：true,点击菜单后是否隐藏所有子菜单。\r\n\t * @property {String} size = [xs|s|m|n|l|g] 默认：n, 按钮大小。\r\n\t * @property {String|Number} width = [] 默认：NaN, 自定义按钮大小。\r\n\t * @property {String|Boolean} show-text = [false|true] 默认：false, 是否显示文字。下下排列结构上图标，下文字。\r\n\t * @property {String} label = [] 默认：'', 主按钮下方的文字\r\n\t * @property {String} icon = [] 默认：icon-plus, 默认图标.\r\n\t * @property {String} bgcolor = [] 默认：\"\", 自定义-背景颜色\r\n\t * @property {String|Number} font-size = [22|23|24|26|28] 默认：22, 文字大小\r\n\t * @property {String} font-color = [] 默认：'', 文字颜色\r\n\t * @property {String|Number} icon-size = [] 默认：'36', 图标大小。\r\n\t * @property {String} color = [] 默认：primary,主题颜色\r\n\t * @property {String|Boolean} absolute = [] 默认：false, 相对父组件定位。\r\n\t * @property {String|Boolean} fixed = [] 默认：true, 绝对定位，根据屏幕定位。\r\n\t * @property {String|Boolean} safe = [true|false] 默认：true,// 是否开启底部安全区域。\r\n\t * @property {String} position = [ topLeft | topRight | bottomRight | bottomLeft|top|bottom|left|right] 默认：bottomRight, 在absolute模式下没有left和right剧中。fixed模式包含所有模式。\r\n\t * @property {Array} offset = [] 默认： [16, 16], 单位upx，自定义偏移量，left,right\r\n\t * @property {Function} click 主按钮点击触发的事件。\r\n\t * @property {Function} change 当有子菜单按钮时，点击子按钮触发事件，并返回顺序Index\r\n\t * @example <tm-flotbutton label=\"发文\" show-text=\"true\" ></tm-flotbutton>\r\n\t */\r\n\timport tmButton from \"@/tm-vuetify/components/tm-button/tm-button.vue\"\r\n\texport default {\r\n\t\tname:\"tm-flotbutton\",\r\n\t\tcomponents:{tmButton},\r\n\t\tprops: {\r\n\t\t\t// 子菜单如果有。\r\n\t\t\tactions: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 同原生btn相同。contact|getPhoneNumber|getUserInfo|launchapp|share|openSetting\r\n\t\t\topenType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 子菜单按钮显示的方向。top | left | bottom | right\r\n\t\t\tactionsPos: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'top'\r\n\t\t\t},\r\n\t\t\t// 始终展开子菜单。\r\n\t\t\tshowActions: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 点击菜单后是否隐藏所有子菜单。\r\n\t\t\tclickActionsHiden: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 同button xs,s,m,n,l,g\r\n\t\t\tsize: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'n'\r\n\t\t\t},\r\n\t\t\twidth: {\r\n\t\t\t\ttype: String | Number,\r\n\t\t\t\tdefault: 100\r\n\t\t\t},\r\n\t\t\theight: {\r\n\t\t\t\ttype: String | Number,\r\n\t\t\t\tdefault: 100\r\n\t\t\t},\r\n\t\t\t// 是否显示询问文字\r\n\t\t\tshowText: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 底部文字。需要和上方同时使用。\r\n\t\t\tlabel: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\ticon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'icon-plus'\r\n\t\t\t},\r\n\r\n\t\t\t// 自定义-背景颜色\r\n\t\t\tbgcolor: {\r\n\t\t\t\ttype: String | Array,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 文字大小。\r\n\t\t\tfontSize: {\r\n\t\t\t\ttype: Number | String,\r\n\t\t\t\tdefault: 22\r\n\t\t\t},\r\n\t\t\t// 定义文字颜色\r\n\t\t\tfontColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'white'\r\n\t\t\t},\r\n\t\t\t// 图标大小。\r\n\t\t\ticonSize: {\r\n\t\t\t\ttype: Number | String,\r\n\t\t\t\tdefault: 36\r\n\t\t\t},\r\n\t\t\t// 主题颜色\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'primary'\r\n\t\t\t},\r\n\t\t\t// 根据像组件定位四个角和上下居中位置。6个位置。\r\n\t\t\tabsolute: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t//是否开启询问安全距离。如果开启了，会计算底部偏差。\r\n\t\t\tsafe:{\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 要的屏幕的宽高定位四个角和上下左右共8个位置\r\n\t\t\tfixed: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// topLeft | topRight | bottomRight | bottomLeft 。在absolute模式下没有left和right剧中。只有top和bottom剧中模式。fixed模式包含所有模式。\r\n\t\t\tposition: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'bottomRight'\r\n\t\t\t},\r\n\t\t\t// 单位upx\r\n\t\t\toffset: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn [16, 16];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 跟随主题色的改变而改变。\r\n\t\t\tfllowTheme:{\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\toffsets: {\r\n\t\t\t\tget: function() {\r\n\t\t\t\t\treturn this.offset;\r\n\t\t\t\t},\r\n\t\t\t\tset: function() {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tthis.offset = [uni.upx2px(this.offset[0]), uni.upx2px(this.offset[1])];\r\n\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\tthis.offset = [0, 0];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcolor_tmeme:function(){\r\n\t\t\t\tif(this.$tm.vx.state().tmVuetify.color!==null&&this.$tm.vx.state().tmVuetify.color && this.fllowTheme){\r\n\t\t\t\t\treturn this.$tm.vx.state().tmVuetify.color;\r\n\t\t\t\t}\r\n\t\t\t\treturn this.color;\r\n\t\t\t},\r\n\t\t\tpos: {\r\n\t\t\t\tget: function() {\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn this.thisPos;\r\n\t\t\t\t},\r\n\t\t\t\tset:function(){\r\n\t\t\t\t\tthis.thisPos = this.posfun();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tposition_info: [],\r\n\t\t\t\tshow: false,\r\n\t\t\t\tisRender:false,\r\n\t\t\t\tthisPos:''\r\n\t\t\t};\r\n\t\t},\r\n\t\t\r\n\t\tasync mounted() {\r\n\t\t\tthis.init();\r\n\t\t\t\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit(){\r\n\t\t\t\tlet t = this;\r\n\t\t\t\tthis.$nextTick(function(){\r\n\t\t\t\t\tt.isRender=true;\r\n\t\t\t\t\tt.position_info = [{width:uni.upx2px(this.width),height:uni.upx2px(this.height)}];\r\n\t\t\t\t\tt.thisPos = t.posfun();\r\n\t\t\t\t})\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t\r\n\t\t\t\twindow.addEventListener('scroll',function(){\r\n\t\t\t\t\tt.thisPos = t.posfun();\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgetsafeJl(){\r\n\t\t\t\tlet sy = uni.getSystemInfoSync();\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\treturn Math.abs(sy.screenHeight - sy.safeArea.bottom);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\treturn Math.abs(sy.windowHeight - sy.safeArea.height);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP\r\n\t\t\t\treturn Math.abs(sy.safeArea.bottom - sy.safeArea.height);\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn 24;\r\n\t\t\t},\r\n\t\t\tposfun(){\r\n\t\t\t\tif (this.absolute && !this.fixed) {\r\n\t\t\t\t\tif (this.position == 'topLeft') {\r\n\t\t\t\t\t\treturn `transform:translateY(${this.offset[1]}px);left:${this.offset[0]}px`;\r\n\t\t\t\t\t} else if (this.position == 'topRight') {\r\n\t\t\t\t\t\treturn `transform:translateY(${this.offset[1]}px);right:${this.offset[0]}px;`\r\n\t\t\t\t\t} else if (this.position == 'bottomRight') {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\treturn `transform:translateY(${this.offset[1]}px);right:${this.offset[0]}px;`;\r\n\t\t\t\t\t} else if (this.position == 'bottomLeft') {\r\n\t\t\t\t\t\treturn `btransform:translateY(${this.offset[1]}px);left:${this.offset[0]}px`;\r\n\t\t\t\t\t} else if (this.position == 'top') {\r\n\t\t\t\t\t\treturn `transform:translateX(${this.offset[0]}px) translateY(${this.offset[1]}px)`\r\n\t\t\t\t\t} else if (this.position == 'bottom') {\r\n\t\t\t\t\t\treturn `transform:translateX(${this.offset[0]}px) translateY(${this.offset[1]}px)`\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.absolute && this.fixed) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (this.position == 'topLeft') {\r\n\t\t\t\t\t\treturn `top:${ this.offset[1]}px;left:${this.offset[0]}px`;\r\n\t\t\t\t\t} else if (this.position == 'topRight') {\r\n\t\t\t\t\t\treturn `top: ${this.offset[1]}px;right: ${this.offset[0]}px`;\r\n\t\t\t\t\t} else if (this.position == 'bottomRight') {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\tlet safbo = this.getsafeJl()\r\n\t\t\t\t\t\tif(!this.safe) safbo=0\r\n\t\t\t\t\t\treturn `bottom: ${this.offset[1]+safbo}px;right: ${this.offset[0]}px;`;\r\n\t\t\t\t\r\n\t\t\t\t\t} else if (this.position == 'bottomLeft') {\r\n\t\t\t\t\t\tlet sy = uni.getSystemInfoSync();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tlet safbo = this.getsafeJl()\r\n\t\t\t\t\t\tif(!this.safe) safbo=0\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\treturn `bottom:${ this.offset[1]+safbo}px;left:${this.offset[0]}px;`;\r\n\t\t\t\t\t} else if (this.position == 'top') {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tlet js = uni.getSystemInfoSync();\r\n\t\t\t\t\t\tlet left = js.windowWidth;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (this.position_info.length > 0) {\r\n\t\t\t\t\t\t\tlet w = this.position_info[0].width;\r\n\t\t\t\t\t\t\treturn `transform:translateX(${this.offset[0]}px) translateY(${this.offset[1]}px);top:0;left:${(left-w)/2}px;`;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t} else if (this.position == 'bottom') {\r\n\t\t\t\t\t\tlet safbo = this.getsafeJl()\r\n\t\t\t\t\t\tif(!this.safe) safbo=0\r\n\t\t\t\t\t\tlet js = uni.getSystemInfoSync();\r\n\t\t\t\t\t\tlet left = js.windowWidth;\r\n\t\t\t\t\t\tif (this.position_info.length > 0) {\r\n\t\t\t\t\t\t\tlet w = this.position_info[0].width;\r\n\t\t\t\t\t\t\tconsole.log(w);\r\n\t\t\t\t\t\t\treturn `transform:translateX(${this.offset[0]}px) translateY(${this.offset[1]}px);bottom:${safbo}px;left:${(left-w)/2}px`;\r\n\t\t\t\t\r\n\t\t\t\t\t\t} else if (this.position == 'left') {\r\n\t\t\t\t\t\t\tlet js = uni.getSystemInfoSync();\r\n\t\t\t\t\t\t\tlet left = js.windowHeight;\r\n\t\t\t\t\t\t\tif (this.position_info.length > 0) {\r\n\t\t\t\t\t\t\t\tlet w = this.position_info[0].height;\r\n\t\t\t\t\t\t\t\treturn `transform:translateX(${this.offset[0]}px) translateY(${this.offset[1]}px);left:0;top:${(left-w)/2}px`;\r\n\t\t\t\t\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t\t} else if (this.position == 'right') {\r\n\t\t\t\t\t\t\tlet js = uni.getSystemInfoSync();\r\n\t\t\t\t\t\t\tlet left = js.windowHeight;\r\n\t\t\t\t\t\t\tif (this.position_info.length > 0) {\r\n\t\t\t\t\t\t\t\tlet w = this.position_info[0].height;\r\n\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\ttransform: `translateX(${this.offset[0]}px) translateY(${this.offset[1]}px)`,\r\n\t\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\t\ttop: (left - w) / 2 + 'px',\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\t`transform:translateX(${this.offset[0]}px) translateY(${this.offset[1]}px);right:0;top:{(left-w)/2}px`;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t},\r\n\t\t\tclick(e) {\r\n\t\t\t\tthis.$emit('click', e);\r\n\t\t\t\tthis.show = !this.show;\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\titemChange(index) {\r\n\t\t\t\t\r\n\t\t\t\tthis.$emit('change', index);\r\n\t\t\t\tif (!this.clickActionsHiden) return;\r\n\t\t\t\tthis.show = !this.show;\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.nobody {\r\n\t\t.menulistAction {\r\n\t\t\t.menulistAction_item {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tanimation: sscsl 0.5s;\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\r\n\t\t\t&.top {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 130upx;\r\n\t\t\t\tleft: 10upx;\r\n\r\n\t\t\t\t.menulistAction_item {\r\n\t\t\t\t\t\r\n\t\t\t\t\tmargin-bottom: 10px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.left {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 10upx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-flow: row;\r\n\t\t\t\tright: 140upx;\r\n\r\n\t\t\t\t.menulistAction_item {\r\n\t\t\t\t\tmargin-left: 10px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.bottom {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 140upx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-flow: column;\r\n\t\t\t\tleft: 10upx;\r\n\r\n\t\t\t\t.menulistAction_item {\r\n\t\t\t\t\tmargin-top: 10px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.right {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 10upx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-flow: row;\r\n\t\t\t\tleft: 140upx;\r\n\t\t\t\r\n\t\t\t\t.menulistAction_item {\r\n\t\t\t\t\tmargin-left: 10px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes sscsl {\r\n\t\tfrom {\r\n\t\t\ttransform: scale(0.5);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\t}\r\n\r\n\t.height100 {\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.fixed,\r\n\t.absolute {\r\n\t\tz-index: 400;\r\n\t\tbottom: 0;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-flotbutton.vue?vue&type=style&index=0&id=3fef0e31&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-flotbutton.vue?vue&type=style&index=0&id=3fef0e31&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775144\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}