{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/source/bill-view/pages/user/editeInfo.vue?c773", "webpack:///D:/source/bill-view/pages/user/editeInfo.vue?1887", "webpack:///D:/source/bill-view/pages/user/editeInfo.vue?413b", "webpack:///D:/source/bill-view/pages/user/editeInfo.vue?ae8e", "uni-app:///pages/user/editeInfo.vue", "webpack:///D:/source/bill-view/pages/user/editeInfo.vue?e27d", "webpack:///D:/source/bill-view/pages/user/editeInfo.vue?a77d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tmMenubars", "tmSheet", "tmInput", "tmButton", "tmAvat<PERSON>", "tmFormVue", "tmPickersDate", "tmIcons", "tmRadio", "data", "userInfo", "id", "nick<PERSON><PERSON>", "sex", "avatar", "birthday", "phone", "token", "sexGroup", "sex1", "sex2", "sys", "computed", "startDate", "date", "endDate", "onShow", "methods", "changeSex", "goBack", "uni", "onChooseAvatar", "avatarUrl", "icon", "url", "filePath", "name", "fileType", "header", "success", "resolve", "reject", "fail", "uploadResult", "console", "formatDate", "dateChange", "toggleGender", "checkstat", "updateUser", "api", "chooseImage", "count", "sizeType", "getDate", "year", "month", "day", "watch", "handler", "immediate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC2K;AAC3K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA8oB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC4FlqB;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAKA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC,gCAEA;gBACAF;kBAAAG;gBAAA;;gBAEA;gBAAA;gBAAA,OACA;kBACAH;oBACAI;oBACAC;oBACAC;oBACAC;oBACAC;sBACA;sBACA;oBACA;oBACAC;sBACA;sBACA;wBACAC;sBACA;wBACAC;sBACA;oBACA;oBACAC;sBACAD;oBACA;kBACA;gBACA;cAAA;gBAtBAE;gBAwBA;gBACA;;gBAEA;gBACAb;;gBAEA;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAc;gBACAd;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAe;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAC;MACA;MAEA;QACAtC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAmC;QACA;UACA;UACApB;QAEA;MACA;MACAA;MACAA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA;IACA;IACAqB;MAAA;MACArB;MAEAA;QACAsB;QACAC;QACAd;UACAT;YACAI;YACAC;YACAC;YACAC;YACAC;cACA;cACA;YACA;YACAC;cACA;cACA;gBACA;cACA;gBACAT;gBACA;cACA;YACA;YACAY;cACAZ;YACA;UACA;QACA;MACA;IACA;IACAwB;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;QACAA;MACA;MACAC;MAAA;MACAC;MACA;IACA;EACA;EACAC;IACA;MACAC;QACA;QACA;QACA;MACA;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtTA;AAAA;AAAA;AAAA;AAAivC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACArwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/editeInfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/editeInfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./editeInfo.vue?vue&type=template&id=27ff3bde&scoped=true&\"\nvar renderjs\nimport script from \"./editeInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./editeInfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./editeInfo.vue?vue&type=style&index=0&id=27ff3bde&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"27ff3bde\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/editeInfo.vue\"\nexport default component.exports", "export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editeInfo.vue?vue&type=template&id=27ff3bde&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$tm.vx.state()\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editeInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editeInfo.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :style=\"{ minHeight: sys.windowHeight + 'px' }\"\r\n\t\t:class=\"[$tm.vx.state().tmVuetify.black ? 'black' : 'grey text ']\">\r\n\t\t<tm-menubars color=\"primary\" title=\"编辑资料\" :showback=\"true\">\r\n\t\t\t<!-- <template #left>\r\n\t\t\t\t<view class=\"pl-24\">\r\n\t\t\t\t\t<tm-button @click=\"goBack\" theme=\"bg-gradient-orange-accent\" size=\"S\"></tm-button>\r\n\t\t\t\t</view>\r\n\t\t\t</template> -->\r\n\t\t</tm-menubars>\r\n\r\n\t\t<view>\r\n\t\t\t<!-- 头像部分 -->\r\n\t\t\t<view class=\"avatar-section\">\r\n\t\t\t\t<tm-sheet :shadow=\"8\" :round=\"8\" :margin=\"[20,20]\">\r\n\t\t\t\t\t<!-- <view class=\"avatar-wrapper\" @tap=\"chooseImage\"> -->\r\n\t\t\t\t\t\t<view class=\"avatar-wrapper\" >\r\n\t\t\t\t\t\t\t<view class=\"avatar-tip\">\r\n\t\t\t\t\t\t\t<button class = \"rounded\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\r\n\t\t\t\t\t\t\t\t<tm-avatar :size=\"140\" :src=\"userInfo.avatar\"></tm-avatar>\r\n\t\t\t\t\t\t<!-- <view class=\"avatar-tip\"> -->\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t <!-- <image class=\"avatar\" src=\"{{avatarUrl}}\"></image> -->\r\n\t\t\t\t\t\t\t</button> \r\n\t\t\t\t\t\t\t<!-- <tm-text label=\"更换头像\" color=\"grey\"></tm-text> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tm-sheet>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 性别选择 -->\r\n\t\t\t<tm-sheet :shadow=\"8\" :round=\"8\" :margin=\"[20,20]\">\r\n\t\t\t\t<view class=\"text-size-s text-weight-b mb-50\">\r\n\t\t\t\t\t性别\r\n\t\t\t\t</view>\r\n\t\t\t\t<tm-radio dense v-model=\"sexGroup.sex1\">\r\n\t\t\t\t\t<template v-slot:default=\"{checkData}\">\r\n\t\t\t\t\t\t<tm-button :theme=\"checkData.checked?'bg-gradient-pink-accent':'grey'\" :plan=\"!checkData.checked\" @click=\"changeSex(0)\">男\r\n\t\t\t\t\t\t</tm-button>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</tm-radio>\r\n\t\t\t\t<tm-radio dense v-model=\"sexGroup.sex2\">\r\n\t\t\t\t\t<template v-slot:default=\"{checkData}\">\r\n\t\t\t\t\t\t<tm-button :theme=\"checkData.checked?'bg-gradient-pink-accent':'grey'\" :plan=\"!checkData.checked\" @click=\"changeSex(1)\">女\r\n\t\t\t\t\t\t</tm-button>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</tm-radio>\r\n\t\t\t</tm-sheet>\r\n\r\n\t\t\t<!-- 基本信息表单 -->\r\n\t\t\t<tm-sheet :shadow=\"8\" :round=\"8\" :margin=\"[20,20]\">\r\n\t\t\t\t<tm-form @submit=\"updateUser\" ref=\"formData\">\r\n\t\t\t\t\t<tm-input name=\"nickname\" inputType=\"nickname\" required title=\"姓名\" v-model=\"userInfo.nickName\" \r\n\t\t\t\t\t\t:left-icon=\"'icon-user-fill'\" :round=\"8\"></tm-input>\r\n\t\t\t\t\t<tm-pickersDate @confirm=\"dateChange\" :default-value=\"userInfo.birthday\" >\r\n\t\t\t\t\t\t<view class=\"fulled\">\r\n\t\t\t\t\t\t\t<tm-input :disabled=\"true\" title=\"生日\"  v-model=\"userInfo.birthday\"  :left-icon=\"'icon-calendaralt-fill'\" right-icon=\"icon-angle-right\"  placeholder=\"请选择生日\" align=\"right\"></tm-input>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</tm-pickersDate>\r\n\t\t\t\t\t<!-- <tm-input name=\"birthday\" title=\"生日\" v-model=\"userInfo.birthday\" \r\n\t\t\t\t\t\t:left-icon=\"'icon-calendaralt-fill'\" :round=\"8\" disabled>\r\n\t\t\t\t\t\t<template #right>\r\n\t\t\t\t\t\t\t<picker mode=\"date\" :value=\"userInfo.birthday\" :start=\"startDate\" :end=\"endDate\" @change=\"bindDateChange\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex flex-row flex-row-center-center\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"text-size-s text-grey\">{{ userInfo.birthday || '请选择生日' }}</text>\r\n\t\t\t\t\t\t\t\t\t<tm-icons name=\"icon-arrow-right\" :font-size=\"32\"></tm-icons>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</tm-input> -->\r\n\r\n\t\t\t\t\t<tm-input name=\"phone\" title=\"手机信息\" v-model=\"userInfo.phone\" \r\n\t\t\t\t\t\t:left-icon=\"'icon-phone'\" :round=\"8\"></tm-input>\r\n\t\t\t\t</tm-form>\r\n\t\t\t</tm-sheet>\r\n\r\n\t\t\t<!-- 保存按钮 -->\r\n\t\t\t<view class=\"save-btn\">\r\n\t\t\t\t<tm-button  theme=\"bg-gradient-orange-accent\" :round=\"24\" block @click=\"updateUser\"\r\n\t\t\t\t\t>保存</tm-button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport tmMenubars from '@/tm-vuetify/components/tm-menubars/tm-menubars.vue';\r\n\timport tmFormVue from '../../tm-vuetify/components/tm-form/tm-form.vue';\r\n\timport tmInput from \"@/tm-vuetify/components/tm-input/tm-input.vue\";\r\n\timport tmPickersDate from \"@/tm-vuetify/components/tm-pickersDate/tm-pickersDate.vue\";\r\n\timport tmSheet from \"@/tm-vuetify/components/tm-sheet/tm-sheet.vue\";\r\n\timport api from \"../../api/index.js\";\r\n\timport urlConfig from '../../utils/config.js';\r\n\timport tmAvatar from \"@/tm-vuetify/components/tm-avatar/tm-avatar.vue\";\r\n\timport tmButton from \"@/tm-vuetify/components/tm-button/tm-button.vue\";\r\n\timport tmIcons from \"@/tm-vuetify/components/tm-icons/tm-icons.vue\";\r\n\timport tmRadio from '../../tm-vuetify/components/tm-radio/tm-radio.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttmMenubars,\r\n\t\t\ttmSheet,\r\n\t\t\ttmInput,\r\n\t\t\ttmButton,\r\n\t\t\ttmAvatar,\r\n\t\t\ttmFormVue,\r\n\t\t\ttmPickersDate,\r\n\t\t\ttmIcons,\r\n\t\t\ttmRadio\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserInfo: {\r\n\t\t\t\t\tid: '',\r\n\t\t\t\t\tnickName: '',\r\n\t\t\t\t\tsex: 0,\r\n\t\t\t\t\tavatar: '',\r\n\t\t\t\t\tbirthday: '',\r\n\t\t\t\t\tphone: '',\r\n\t\t\t\t\ttoken: ''\r\n\t\t\t\t},\r\n\t\t\t\tsexGroup: {\r\n\t\t\t\t\tsex1: false,\r\n\t\t\t\t\tsex2: false\r\n\t\t\t\t},\r\n\t\t\t\tsys: uni.getSystemInfoSync()\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tstartDate() {\r\n\t\t\t\tconst date = new Date();\r\n\t\t\t\tdate.setFullYear(date.getFullYear() - 100);\r\n\t\t\t\treturn this.formatDate(date);\r\n\t\t\t},\r\n\t\t\tendDate() {\r\n\t\t\t\treturn this.formatDate(new Date());\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.judgeLogin((res ) => {\r\n\t\t\t\tthis.userInfo = res;\r\n\t\t\t\tthis.changeSex(this.userInfo.sex);\r\n\t\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tchangeSex(val) {\r\n\t\t\t\t// 更新性别值\r\n\t\t\t\tthis.userInfo.sex = val;\r\n\t\t\t\t// 更新选中状态\r\n\t\t\t\tthis.sexGroup.sex1 = val === 0;\r\n\t\t\t\tthis.sexGroup.sex2 = val === 1;\r\n\t\t\t},\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\tasync onChooseAvatar(e) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取选择的头像临时路径\r\n\t\t\t\t\tconst { avatarUrl } = e.detail;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示上传中提示\r\n\t\t\t\t\tuni.$tm.toast('头像上传中...', {icon: 'loading'});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 上传头像到服务器\r\n\t\t\t\t\tconst uploadResult = await new Promise((resolve, reject) => {\r\n\t\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\t\turl: urlConfig + 'jeecg-boot/sys/common/upload',\r\n\t\t\t\t\t\t\tfilePath: avatarUrl,\r\n\t\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\t\tfileType: 'image',\r\n\t\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t\t'X-Access-Token': this.userInfo.token,\r\n\t\t\t\t\t\t\t\t'content-type': 'multipart/form-data'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tsuccess: (response) => {\r\n\t\t\t\t\t\t\t\tconst data = JSON.parse(response.data);\r\n\t\t\t\t\t\t\t\tif (data.success) {\r\n\t\t\t\t\t\t\t\t\tresolve(data.message);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\treject(new Error(data.message || '上传失败'));\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\t\t\treject(new Error('上传失败'));\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新用户头像\r\n\t\t\t\t\tthis.userInfo.avatar = uploadResult;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 立即更新本地存储\r\n\t\t\t\t\tuni.setStorageSync('userInfo', this.userInfo);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示成功提示\r\n\t\t\t\t\tuni.$tm.toast('头像更新成功');\r\n\t\t\t\t\t\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('头像上传失败:', error);\r\n\t\t\t\t\tuni.$tm.toast(error.message || '头像上传失败');\r\n\t\t\t\t\t// 如果上传失败，保持原头像不变\r\n\t\t\t\t\tthis.userInfo.avatar = this.userInfo.avatar || '';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tformatDate(date) {\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\t\tconst day = date.getDate().toString().padStart(2, '0');\r\n\t\t\t\treturn `${year}-${month}-${day}`;\r\n\t\t\t},\r\n\t\t\tdateChange(e) {\r\n\t\t\t\tthis.userInfo.birthday = e.year + \"-\" +e.month + \"-\" +e.day\r\n\t\t\t},\r\n\t\t\ttoggleGender(gender){\r\n\t\t\t\tthis.userInfo.sex = gender;\r\n\t\t\t},\r\n\t\t\tcheckstat(){\r\n\t\t\t\t// var loginInfo = util.checklogin();\r\n\t\t\t\t// this.userInfo = loginInfo.userInfo;\r\n\t\t\t\t// this.openId = loginInfo.openId;\r\n\t\t\t\t// this.token = loginInfo.token;\r\n\t\t\t\t// if(this.userInfo){\r\n\t\t\t\t// \tthis.age = getAge(this.userInfo.birthday);\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t\tupdateUser(){\r\n\t\t\t\tthis.userInfo.openId = this.openId;\r\n\t\t\t\t\r\n\t\t\t\tlet updateUser = {\r\n\t\t\t\t\tid:this.userInfo.id,\r\n\t\t\t\t\tnickName:this.userInfo.nickName,\r\n\t\t\t\t\tsex:this.userInfo.sex,\r\n\t\t\t\t\tavatar:this.userInfo.avatar,\r\n\t\t\t\t\tbirthday:this.userInfo.birthday\t\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tapi.updateUser(updateUser).then(res=>{\r\n\t\t\t\t\tif(res.data.success){\r\n\t\t\t\t\t\t//uni.setStorageSync('userInfo', parSetData(this.userInfo));\r\n\t\t\t\t\t\tuni.$tm.toast('用户信息修改成功！')\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tuni.setStorageSync('userInfo', this.userInfo);\r\n\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t// success: function() {\r\n\t\t\t\t\t// \tlet pages = getCurrentPages();\r\n\t\t\t\t\t// \tlet beforePage = pages[pages.length - 1]\r\n\t\t\t\t\t//     beforePage.$vm.mounted();\r\n\t\t\t\t\t// }\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchooseImage() {\r\n\t\t\t\tuni.$tm.toast('只可上传一张图片,将自动替换为新选择的')\r\n\t\t\t\t\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tsizeType: 'compressed',\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\t\turl: urlConfig + 'jeecg-boot/sys/common/upload',\r\n\t\t\t\t\t\t\tfilePath: res.tempFilePaths[0],\r\n\t\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\t\tfileType:'image',\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t\t'X-Access-Token':userInfo.token,\r\n\t\t\t\t\t\t\t\t'content-type': 'multipart/form-data'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tsuccess: response => {\r\n\t\t\t\t\t\t\t\tlet data = JSON.parse(response.data)\r\n\t\t\t\t\t\t\t\tif (data.success) {\r\n\t\t\t\t\t\t\t\t\tthis.userInfo.avatar = data.result.obsObjectUrl;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.$tm.toast(data.message)\r\n\t\t\t\t\t\t\t\t\tthis.userInfo.avatar = '';\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail:response=>{\r\n\t\t\t\t\t\t\t\tuni.$tm.toast('上传失败')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetDate(type) {\r\n\t\t\t\tconst date = new Date();\r\n\t\t\t\tlet year = date.getFullYear();\r\n\t\t\t\tlet month = date.getMonth() + 1;\r\n\t\t\t\tlet day = date.getDate();\r\n\t\t\t\t\r\n\t\t\t\tif (type === 'start') {\r\n\t\t\t\t\tyear = year - 60;\r\n\t\t\t\t} else if (type === 'end') {\r\n\t\t\t\t\tyear = year;\r\n\t\t\t\t}\r\n\t\t\t\tmonth = month > 9 ? month : '0' + month;;\r\n\t\t\t\tday = day > 9 ? day : '0' + day;\r\n\t\t\t\treturn `${year}-${month}-${day}`;\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t'userInfo.sex': {\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\t// 同步性别选择状态\r\n\t\t\t\t\tthis.sexGroup.sex1 = newVal === 0;\r\n\t\t\t\t\tthis.sexGroup.sex2 = newVal === 1;\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.avatar-section {\r\n\t.avatar-wrapper {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tpadding: 40rpx 0;\r\n\t\t\r\n\t\t.avatar-tip {\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.gender-section {\r\n\tpadding: 30rpx;\r\n\t\r\n\t.gender-options {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\tmargin-top: 20rpx;\r\n\t\t\r\n\t\t.tm-button {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tgap: 10rpx;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.save-btn {\r\n\tmargin-top: 40rpx;\r\n}\r\n\r\n.flex {\r\n\tdisplay: flex;\r\n}\r\n.flex-row {\r\n\tflex-direction: row;\r\n}\r\n.flex-row-center-center {\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editeInfo.vue?vue&type=style&index=0&id=27ff3bde&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editeInfo.vue?vue&type=style&index=0&id=27ff3bde&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775134\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}