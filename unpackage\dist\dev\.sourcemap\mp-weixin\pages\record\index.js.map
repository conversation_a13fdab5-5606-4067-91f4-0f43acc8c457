{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/source/bill-view/pages/record/index.vue?92c0", "webpack:///D:/source/bill-view/pages/record/index.vue?2045", "webpack:///D:/source/bill-view/pages/record/index.vue?17d3", "webpack:///D:/source/bill-view/pages/record/index.vue?85a5", "uni-app:///pages/record/index.vue", "webpack:///D:/source/bill-view/pages/record/index.vue?6461", "webpack:///D:/source/bill-view/pages/record/index.vue?509d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tmTabs", "tmMenubars", "tmSwitchList", "tmPullBottom", "tmPoup", "tmSheet", "data", "item_2", "item_1", "text", "width", "color", "resh", "loading", "opens", "time", "list", "activeIndex", "show", "word", "type", "er", "heightHome", "fliter", "pageNo", "pageSize", "types", "listTypeOut", "icon", "id", "iconSize", "fontColor", "listTypeIn", "books", "showremark", "remarks", "total", "created", "onLoad", "onShow", "mounted", "methods", "action", "uni", "url", "title", "content", "success", "that", "console", "loadH", "info", "loadTypeName", "t", "loadTypeIcon", "getdata", "loadList", "userId", "loadListN", "loadType", "selectType", "<PERSON><PERSON><PERSON>", "open", "selectTime", "change", "viewRemarks"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACc;;;AAGlE;AAC2K;AAC3K,gBAAgB,qLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpCA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,upBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoD9pB;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAd;MACAe;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC,cACA;QAAAC;QAAAnB;QAAAE;QAAAkB;MAAA,GACA;QAAAD;QAAAnB;QAAAqB;QAAAnB;QAAAkB;MAAA,GACA;QAAAD;QAAAnB;QAAAE;QAAAoB;QAAAF;MAAA,GACA;QAAAD;QAAAnB;QAAAE;QAAAkB;MAAA,EACA;MACAG,aACA;QAAAJ;QAAAnB;QAAAE;QAAAkB;MAAA,GACA;QAAAD;QAAAnB;QAAAqB;QAAAnB;QAAAkB;MAAA,GACA;QAAAD;QAAAnB;QAAAE;QAAAkB;MAAA,EACA;MACAI;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;;IAIA;EAEA;EACAC;IAEA;EAGA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;QACA;QACAC;QACAA;UACAC;QACA;MACA;QACA;QACAD;UACAE;UACAC;UACAC;YACA;cACAC;gBACA;kBACAA;gBACA;cACA;YACA;cACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACAP;QACAI;UAAA;UACA;UACAI;YAAA;YACAF;YACA;YACAD;UACA;QACA;MACA;IACA;IACAI;MACA;QACA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAD;QACA;MACA;MACA;QACAA;MACA;MACA;IAEA;IACAE;MACA;QACA;QACA;MACA;QACA;UACA;UACA;QACA;UACA;UACAZ;QACA;MACA;QACA;MACA;IACA;IACAa;MAAA;MACA;QACA;UACAC;UACApC;UACAG;UACAC;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAiC;MAAA;MACA;QACA;UACAD;UACApC;UACAG;UACAC;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAkC;MAAA;MACA;QAEA;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA,EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9UA;AAAA;AAAA;AAAA;AAAqtC,CAAgB,sqCAAG,EAAC,C;;;;;;;;;;;ACAzuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/record/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/record/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=125f448d&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"125f448d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/record/index.vue\"\nexport default component.exports", "export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=125f448d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$tm.vx.state()\n  var g1 = _vm.$tm.vx.state()\n  var g2 = !g1.tmVuetify.black\n    ? _vm.$tm.vx.state().tmVuetify.color || \"primary\"\n    : null\n  var l0 = _vm.resh\n    ? _vm.__map(_vm.data, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.loadTypeIcon(item)\n        var m1 = _vm.loadTypeName(item)\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :style=\"{ minHeight: sys.windowHeight + 'px' }\"\r\n\t\t:class=\"[$tm.vx.state().tmVuetify.black ? 'black maina' : 'grey text maina']\">\r\n\t\t<view>\r\n\t\t\t<tm-menubars color=\"primary\" title=\"记账详情\" :shadow=\"0\">\r\n\t\t\t</tm-menubars>\r\n\t\t\t<view>\r\n\t\t\t\t<view :class=\"[$tm.vx.state().tmVuetify.black ? 'black' : $tm.vx.state().tmVuetify.color || 'primary']\">\r\n\t\t\t\t\r\n\t\t\t\t\t<tm-tabs :fllowTheme=\"false\" bg-color=\"amber\" color=\"red\" font-size=\"38\" active-font-size=\"38\"  @change=\"change\" v-model=\"activeIndex\"\r\n\t\t\t\t\t\t:list=\"list\" range-key=\"title\"></tm-tabs>\r\n\t\t\t\t<!-- <view style=\"height: 84rpx;\"></view>-->\r\n\t\t\t\t</view> \r\n\t\t\t\t<view class=\"fixed fulled  overflow\" style=\"z-index: 8;\" v-if=\"resh\">\r\n\t\t\t\t\t<tm-pullBottom :height=\"heightHome\" :loading.sync=\"loading\" @refresh=\"getdata\">\r\n\t\t\t\t\t\t<tm-switchList color=\"blue\" :rightLabel=\"'￥'+item.amount\" :icon=\"loadTypeIcon(item)\" :on='false'\r\n\t\t\t\t\t\t\t@actionsClick=\"((e)=>{action(e, item)})\" :title=\"loadTypeName(item)\"\r\n\t\t\t\t\t\t\t@click=\"viewRemarks(item)\" v-for=\"(item,index) in data\" :label=\"item.transactionDate+' '+item.ledgerId_dictText\" :key=\"index\"\r\n\t\t\t\t\t\t\t:actions=\"item.share? item: item_1\" :rightIcon=\"item.share?'icon-share1':''\">\r\n\t\t\t\t\t\t</tm-switchList>\r\n\t\t\t\t\t</tm-pullBottom>\r\n\t\t\t\t\t<tm-poup v-model=\"showremark\" position=\"bottom\" height=\"300\">\r\n\t\t\t\t\t\t<tm-sheet color=\"blue text\" :shadow=\"24\">\r\n\t\t\t\t\t\t\t<view class=\"text-size-s text-weight-b mb-24\">\r\n\t\t\t\t\t\t\t\t<text>\r\n\t\t\t\t\t\t\t\t\t备注: {{ remarks }}\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</tm-sheet>\r\n\t\t\t\t\t</tm-poup>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view :class=\"[$tm.vx.state().tmVuetify.black ? 'black' : $tm.vx.state().tmVuetify.color || 'primary']\">\r\n\t\t\t\t\t<view class=\" py-32\" style=\"\"\r\n\t\t\t\t\t\t:class=\"[$tm.vx.state().tmVuetify.black ? 'grey-darken-5 bk' : $tm.vx.state().tmVuetify.color || 'primary']\">\r\n\t\t\t\t\t\t<view class=\"text-size-s text-align-center text-white\">\r\n\t\t\t\t\t\t\t<text>{{ transactionDate }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// https://roundicons.com/icon-packs/free-christmas-icons/\r\n\timport tmMenubars from '@/tm-vuetify/components/tm-menubars/tm-menubars.vue';\r\n\timport tmTabs from \"@/tm-vuetify/components/tm-tabs/tm-tabs.vue\"\r\n\timport tmSwitchList from '@/tm-vuetify/components/tm-switchList/tm-switchList.vue';\r\n\timport tmPullBottom from \"@/tm-vuetify/components/tm-pullBottom/tm-pullBottom.vue\"\r\n\timport tmPoup from \"@/tm-vuetify/components/tm-poup/tm-poup.vue\"\r\n\timport tmSheet from \"@/tm-vuetify/components/tm-sheet/tm-sheet.vue\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttmTabs,\r\n\t\t\ttmMenubars,\r\n\t\t\ttmSwitchList,\r\n\t\t\ttmPullBottom,\r\n\t\t\ttmPoup,\r\n\t\t\ttmSheet\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\titem_2: [],\r\n\t\t\t\titem_1: [{\r\n\t\t\t\t\t\ttext: \"编辑\",\r\n\t\t\t\t\t\twidth: 140,\r\n\t\t\t\t\t\tcolor: 'black'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: \"删除\",\r\n\t\t\t\t\t\twidth: 110,\r\n\t\t\t\t\t\tcolor: 'red'\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\tresh: false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\topens: false,\r\n\t\t\t\ttime: '',\r\n\t\t\t\tlist: ['支出', '收入'],\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\tshow: true,\r\n\t\t\t\tword: '',\r\n\t\t\t\ttype: {},\r\n\t\t\t\tdata: [],\r\n\t\t\t\ter: 0,\r\n\t\t\t\theightHome: 1200,\r\n\t\t\t\tfliter: {\r\n\t\t\t\t\tpageNo: 1,\r\n\t\t\t\t\tpageSize: 15\r\n\t\t\t\t},\r\n\t\t\t\ttypes: [],\r\n\t\t\t\tlistTypeOut:[\r\n\t\t\t\t\t\t{icon:'icon-shopping-cart-fill',text:'网购',color:'blue',id:\"111\"},\r\n\t\t\t\t\t\t{icon:'icon-weibo',text:'微博',iconSize:40,color:'blue',id:\"112\"},\r\n\t\t\t\t\t\t{icon:'icon-home',text:'房贷',color:'green',fontColor:'green',id:\"113\"},\r\n\t\t\t\t\t\t{icon:'icon-user-group-fill',text:'人情世故',color:'red',id:\"114\"},\r\n\t\t\t\t\t],\r\n\t\t\t\tlistTypeIn:[\r\n\t\t\t\t\t\t{icon:'icon-dollar',text:'工资',color:'blue',id:\"211\"},\r\n\t\t\t\t\t\t{icon:'icon-inboxin-fill',text:'奖金',iconSize:40,color:'blue',id:\"212\"},\r\n\t\t\t\t\t\t{icon:'icon-user-group-fill',text:'人情世故',color:'red',id:\"213\"},\r\n\t\t\t\t\t],\r\n\t\t\t\tbooks: [],\r\n\t\t\t\tshowremark: false,\r\n\t\t\t\tremarks: '',\r\n\t\t\t\ttotal: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.sys = uni.getSystemInfoSync();\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.bottom = 55\r\n\t\t\t// #endif\r\n\t\t\t// this.loadH();\r\n\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.top = uni.upx2px(150);\r\n\t\t\t// #endif\r\n\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.resh = false;\r\n\t\t\tthis.loadType();\r\n\t\t\t//this.loadBook();\r\n\t\t\tthis.getdata('pull');\r\n\t\t\tthis.opens = false\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.resh = false;\r\n\t\t\tthis.loadType();\r\n\t\t\t//this.loadBook();\r\n\t\t\tthis.getdata('pull');\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\taction(e, item) {\r\n\t\t\t\tthis.opens = false\r\n\t\t\t\tif (e.index == 0) {\r\n\t\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t\t//     url:'/pages/index/index?id='+item.id\r\n\t\t\t\t\t// });\r\n\t\t\t\t\tuni.setStorageSync('billId', item);\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet that = this;\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '确定删除该账单明细',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tthat.$api.deleteBill(item.id).then(res => {\r\n\t\t\t\t\t\t\t\t\tif(res.data.success){\r\n\t\t\t\t\t\t\t\t\t\tthat.getdata()\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tloadH() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.getSystemInfo({\r\n\t\t\t\t\tsuccess: function(res) { // res - 各种参数\r\n\t\t\t\t\t\tlet info = uni.createSelectorQuery().select(\".maina\"); // 获取某个元素\r\n\t\t\t\t\t\tinfo.boundingClientRect(function(data) { //data - 各种参数\r\n\t\t\t\t\t\t\tconsole.log(data.height) // 获取元素宽度\r\n\t\t\t\t\t\t\tlet view = (res.windowHeight - data.height) * 2\r\n\t\t\t\t\t\t\tthat.heightHome = 1200\r\n\t\t\t\t\t\t}).exec()\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tloadTypeName(val) {\r\n\t\t\t\tif(!val){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet t = '未知明细';\r\n\t\t\t\tthis.types.forEach(i => {\r\n\t\t\t\t\tif (val.categoryId == i.id ) {\r\n\t\t\t\t\t\tt = i.text;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn t;\r\n\t\t\t},\r\n\t\t\t// loadBookName(val) {\r\n\t\t\t// \tlet t = val.time;\r\n\t\t\t// \tfor (let i = 0; i < this.books.length; i++) {\r\n\t\t\t// \t\tif (this.books[i].id == val.bid) {\r\n\t\t\t// \t\t\tt = t + '\t' + this.books[i].name;\r\n\t\t\t// \t\t\tbreak;\r\n\t\t\t// \t\t}\r\n\t\t\t// \t}\r\n\t\t\t// \treturn t;\r\n\t\t\t// },\r\n\t\t\tloadTypeIcon(val) {\r\n\t\t\t\tlet t = '';\r\n\t\t\t\tthis.types.forEach(i => {\r\n\t\t\t\t\tif (i.id == val.categoryId) {\r\n\t\t\t\t\t\tt = i.icon;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tif (t == '') {\r\n\t\t\t\t\tt = 'icon-gem'\r\n\t\t\t\t}\r\n\t\t\t\treturn t;\r\n\r\n\t\t\t},\r\n\t\t\tgetdata(e) {\r\n\t\t\t\tif (e == 'pull') {\r\n\t\t\t\t\tthis.fliter.pageNo = 1;\r\n\t\t\t\t\tthis.loadList();\r\n\t\t\t\t} else if (e == 'bottom') {\r\n\t\t\t\t\tif (this.total > this.data.length) {\r\n\t\t\t\t\t\tthis.fliter.pageNo = this.fliter.pageNo + 1;\r\n\t\t\t\t\t\tthis.loadListN()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t\tuni.$tm.toast('已经是全部了');\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.loadList();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tloadList() {\r\n\t\t\t\tthis.judgeLogin(res => {\r\n\t\t\t\t\tthis.$api.getBillList({\r\n\t\t\t\t\t\tuserId: res.id,\r\n\t\t\t\t\t\ter: this.er,\r\n\t\t\t\t\t\tpageNo: this.fliter.pageNo,\r\n\t\t\t\t\t\tpageSize: this.fliter.pageSize\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tthis.data = res.data.result.records\r\n\t\t\t\t\t\tthis.total = res.data.result.total\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t\tthis.resh = true;\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tloadListN() {\r\n\t\t\t\tthis.judgeLogin(res => {\r\n\t\t\t\t\tthis.$api.getBillList({\r\n\t\t\t\t\t\tuserId: res.id,\r\n\t\t\t\t\t\ter: this.er,\r\n\t\t\t\t\t\tpageNo: this.fliter.pageNo,\r\n\t\t\t\t\t\tpageSize: this.fliter.pageSize\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tthis.data = this.data.concat(res.data.result.records)\r\n\t\t\t\t\t\tthis.total = res.data.result.total\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t\tthis.resh = true;\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tloadType() {\r\n\t\t\t\tthis.$api.getTypeList(this.er).then(res => {\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(res.data.success){\r\n\t\t\t\t\t\tthis.types = res.data.result\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// loadBook() {\r\n\t\t\t// \tthis.judgeLogin(res => {\r\n\t\t\t// \t\tthis.$api.getBookList({\r\n\t\t\t// \t\t\townerId: res.id\r\n\t\t\t// \t\t}).then(res => {\r\n\t\t\t// \t\t\tconsole.log(res)\r\n\t\t\t// \t\t\tthis.books = res.data.result;\r\n\t\t\t// \t\t})\r\n\t\t\t// \t})\r\n\r\n\t\t\t// },\r\n\t\t\tselectType(e) {\r\n\t\t\t\tthis.type = e.data\r\n\t\t\t},\r\n\t\t\tcloseKey() {\r\n\t\t\t\tthis.show = false;\r\n\t\t\t},\r\n\t\t\topen() {\r\n\t\t\t\tthis.show = true;\r\n\t\t\t},\r\n\t\t\tselectTime(e) {\r\n\t\t\t\tthis.time = e.year + \"/\" + e.month + \"/\" + e.day\r\n\t\t\t},\r\n\t\t\t// getCurrentTime() {\r\n\t\t\t// \t//获取当前时间并打印\r\n\t\t\t// \tlet yy = new Date().getFullYear();\r\n\t\t\t// \tlet mm = new Date().getMonth() + 1;\r\n\t\t\t// \tlet dd = new Date().getDate();\r\n\t\t\t// \tlet hh = new Date().getHours();\r\n\t\t\t// \tlet mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();\r\n\t\t\t// \tlet ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();\r\n\t\t\t// \tthis.time = yy + '/' + mm + '/' + dd;\r\n\t\t\t// },\r\n\t\t\tchange(e) {\r\n\t\t\t\tthis.word = ''\r\n\t\t\t\tthis.type = {}\r\n\t\t\t\tthis.er = e\r\n\t\t\t\tthis.fliter.pageNo = 1;\r\n\t\t\t\tthis.loadType();\r\n\t\t\t\tthis.loadList();\r\n\t\t\t},\r\n\t\t\tviewRemarks(item) {\r\n\t\t\t\tthis.showremark = true;\r\n\t\t\t\tif (null != item.notes && undefined != item.notes) {\r\n\t\t\t\t\tthis.remarks = item.notes;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.remarks = ''\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// confirm(val) {\r\n\t\t\t// \tthis.show = true;\r\n\t\t\t// \tthis.judgeLogin(() => {\r\n\t\t\t// \t\tthis.$ajax.post('/bill/save', {\r\n\t\t\t// \t\t\ttype: this.type.id,\r\n\t\t\t// \t\t\ter: this.er,\r\n\t\t\t// \t\t\tvalue: val,\r\n\t\t\t// \t\t\tuid: this.$store.state.userInfo.id,\r\n\t\t\t// \t\t\ttime: this.time\r\n\t\t\t// \t\t}).then(res => {\r\n\t\t\t// \t\t\tthis.word = ''\r\n\t\t\t// \t\t\tthis.type = {}\r\n\t\t\t// \t\t})\r\n\t\t\t// \t});\r\n\r\n\t\t\t// }\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage,\r\n\tbody {\r\n\t\tmin-height: 100%;\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped></style>\r\n", "import mod from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775093\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}