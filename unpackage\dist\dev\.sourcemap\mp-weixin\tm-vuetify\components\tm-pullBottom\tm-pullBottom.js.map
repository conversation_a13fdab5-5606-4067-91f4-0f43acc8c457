{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-pullBottom/tm-pullBottom.vue?7dec", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-pullBottom/tm-pullBottom.vue?3ebc", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-pullBottom/tm-pullBottom.vue?eef4", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-pullBottom/tm-pullBottom.vue?b646", "uni-app:///tm-vuetify/components/tm-pullBottom/tm-pullBottom.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-pullBottom/tm-pullBottom.vue?fb8c", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-pullBottom/tm-pullBottom.vue?d6bb"], "names": ["components", "tmIcons", "tmLoadding", "name", "props", "height", "type", "default", "pullY", "bottomY", "color", "loading", "finish", "disabled", "watch", "data", "activeHeight", "isRefresh", "freshing", "showScrollPic", "isPullDown", "bottomLoadding", "computed", "mounted", "wsz", "methods", "onRefresh", "setTimeout", "t", "onPulling", "onAbort", "onRestore", "susscess", "pullChangeOk", "pullBottom"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,+pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgErrB;EACAA;IAAAC;IAAAC;EAAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;EAEA;EACAO;IACAH;MAEA;QACA;QACA;MACA;IACA;EAEA;EACAI;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC,WAEA;EACAC;IACA;IACA;IACA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cAAA,IACA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAAAC;cACA;YAAA;cAEA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;QACAC;QACAA;QACA;QACAA;QACAA;MACA;IAEA;IACAC;MAEA;QAEA;MACA;MACA;MACA;MACA;MAKA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IAEA;IACAC;MACA;IAEA;IACA;IACAC;MACA;MACA;IAEA;IACAC;MAEA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvMA;AAAA;AAAA;AAAA;AAAgxC,CAAgB,ssCAAG,EAAC,C;;;;;;;;;;;ACApyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-pullBottom/tm-pullBottom.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-pullBottom.vue?vue&type=template&id=23db1152&scoped=true&\"\nvar renderjs\nimport script from \"./tm-pullBottom.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-pullBottom.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-pullBottom.vue?vue&type=style&index=0&id=23db1152&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"23db1152\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-pullBottom/tm-pullBottom.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-pullBottom.vue?vue&type=template&id=23db1152&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-pullBottom.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-pullBottom.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"tm-pullBottom\">\r\n\t\t<scroll-view \r\n\t\tclass=\"tm-pullBottom-sroll\"\r\n\t\t:refresher-enabled=\"disabled\"\r\n\t\t:refresher-threshold=\"pullY\"\r\n\t\t:refresher-triggered=\"isRefresh\"\r\n\t\t:scroll-y=\"true\"\r\n\t\trefresher-default-style=\"none\"\r\n\t\t:lower-threshold=\"bottomY\"\r\n\t\t@scrolltolower=\"pullBottom\"\r\n\t\t@refresherpulling=\"onPulling\"\r\n\t\t@refresherrefresh=\"onRefresh\" \r\n\t\t@refresherrestore=\"onRestore\"\r\n\t\t@refresherabort=\"onAbort\"\r\n\t\t:style=\"{\r\n\t\t\theight:activeHeight+'px'\r\n\t\t}\">\r\n\t\t\t<view v-if=\"bottomLoadding==true\" class=\"tm-pullBottom-top flex-center flex-col\">\r\n\t\t\t\t<view v-if=\"loading\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<slot name=\"pull\" v-if=\"isPullDown==true&&showScrollPic\">\r\n\t\t\t\t\t\t\t<view  class=\"tm-pullBottom-top-icon flex-center pull\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<tm-icons :color=\"color\" name=\"icon-long-arrow-up\"></tm-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex-center tm-pullBottom-top-text text-size-n\"\r\n\t\t\t\t\t\t\t:class=\"[`text-${color}`]\">继续下拉刷新</view>\r\n\t\t\t\t\t\t</slot>\r\n\t\t\t\t\t\t<slot name=\"pullReresh\" v-if=\"isPullDown==false&&showScrollPic\">\r\n\t\t\t\t\t\t\t<view  class=\"tm-pullBottom-top-icon flex-center \"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<tm-icons :color=\"color\" name=\"icon-long-arrow-up\"></tm-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex-center tm-pullBottom-top-text text-size-n\"\r\n\t\t\t\t\t\t\t:class=\"[`text-${color}`]\" >松开刷新</view>\r\n\t\t\t\t\t\t</slot>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<tm-loadding v-if=\"showScrollPic==false\" ></tm-loadding>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<slot name=\"default\"></slot>\r\n\t\t\t<tm-loadding v-if=\"bottomLoadding==false&&loading\" ></tm-loadding>\r\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\r\n\t/**\r\n\t * 上拉触底刷新\r\n\t * @property {String|Number} height = [] 默认0，默认0使用父高度。\r\n\t * @property {Number} pullY = [] 默认80，下拉多长的距离执行刷新。\r\n\t * @property {Number} bottomY = [] 默认0，离底部多高度多少执行加载\r\n\t * @property {String} color = [] 默认primary，主题色。\r\n\t * @property {Boolean} loading = [] 默认 false，，需要loading.sync执行双向绑定，加载状态，true，加载中。false加载完成。\r\n\t * @property {Boolean} disabled = [] 默认 true，，是否启用下拉刷新，不影响触底刷新功能。\r\n\t * @property {Boolean} finish = [] 默认 false，是否数据没有了。如果为true，触底后将不会触发刷新操作。\r\n\t * @property {Function} refresh 当下拉或者触底时，触发此函数。参数e=pull为下拉刷新，bottom为触底刷新。\r\n\t * @example <tm-pullBottom :loading.sync=\"loading\" @refresh=\"getdata\"></tm-pullBottom>\r\n\t */\r\n\timport tmIcons from \"@/tm-vuetify/components/tm-icons/tm-icons.vue\"\r\n\timport tmLoadding from \"@/tm-vuetify/components/tm-loadding/tm-loadding.vue\"\r\n\t\n\texport default {\r\n\t\tcomponents:{tmIcons,tmLoadding},\r\n\t\tname:\"tm-pullBottom\",\r\n\t\tprops:{\r\n\t\t\t// 高度，默认为0时，自动使用父组件的高度.\r\n\t\t\theight: {\r\n\t\t\t\ttype: String | Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tpullY:{\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 80\r\n\t\t\t},\r\n\t\t\tbottomY:{\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'primary'\r\n\t\t\t},\r\n\t\t\tloading:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t},\r\n\t\t\t// 是否没有更多数据了。\r\n\t\t\tfinish:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t},\r\n\t\t\tdisabled:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:true\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\twatch:{\r\n\t\t\tloading:function(newval){\r\n\t\t\t\t\r\n\t\t\t\tif(newval==false){\r\n\t\t\t\t\t// 结束操作。\r\n\t\t\t\t\tthis.susscess();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tactiveHeight: 0,\r\n\t\t\t\tisRefresh:false,//是否触发了下拉刷新区域。\r\n\t\t\t\tfreshing:false,//是否刷新 中。\r\n\t\t\t\tshowScrollPic:false,//是否拖动了下拉区域，显示图标。停止不显示。\r\n\t\t\t\tisPullDown:false,//是否下正确的下拉刷新区域。\r\n\t\t\t\tbottomLoadding:true,//当前是底部还是顶部刷新操作。false为底部。true为顶部。\n\t\t\t};\n\t\t},\r\n\t\tcomputed:{\r\n\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.guid = uni.$tm.guid();\r\n\t\t\tlet t = this;\r\n\t\t\tthis.$nextTick(async function() {\r\n\t\t\t\tthis.activeHeight = uni.upx2px(this.height);\r\n\t\t\t\tif (!this.activeHeight) {\r\n\t\t\t\t\tlet wsz = await this.$Querey(\".tm-pullBottom\",this).catch(e=>{})\r\n\t\t\t\t\tthis.activeHeight = wsz[0].height||150;\r\n\t\t\t\t}\r\n\t\t\t\tif(this.loading===true){\r\n\t\t\t\t\tthis.$emit('update:loading',false)\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonRefresh(e) {\r\n\t\t\t\tlet t = this;\r\n\t\t\t\tthis.isRefresh = true;\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tt.isRefresh = false;\r\n\t\t\t\t\tt.showScrollPic = false;\r\n\t\t\t\t\tif(t.freshing==true) return;\r\n\t\t\t\t\tt.freshing = true;\r\n\t\t\t\t\tt.pullChangeOk();\r\n\t\t\t\t}, 200);\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tonPulling(e){\r\n\t\t\t\t\r\n\t\t\t\tif(this.loading === false){\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.$emit('update:loading',true)\r\n\t\t\t\t}\r\n\t\t\t\tthis.bottomLoadding = true;\r\n\t\t\t\tthis.showScrollPic = true;//显示刷新 图标。\r\n\t\t\t\tlet dy = e.target.dy || 0\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tdy = e.target.deltaY;\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\tif(dy < this. pullY){\r\n\t\t\t\t\tthis.isPullDown = true;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.isPullDown = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonAbort(e){\r\n\t\t\t\tthis.$emit('update:loading',false)\r\n\t\t\t\tthis.showScrollPic = false;//显示刷新 图标。\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tonRestore(e){\r\n\t\t\t\tthis.isRefresh = 'restore'; // 需要重置\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t// 结束操作。\r\n\t\t\tsusscess(){\r\n\t\t\t\tthis.freshing = false;\r\n\t\t\t\tthis.$emit('update:loading',false)\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tpullChangeOk(){\r\n\t\t\t\t\r\n\t\t\t\tthis.$emit(\"refresh\",'pull')\r\n\t\t\t},\r\n\t\t\t// 触底加载中。\r\n\t\t\tpullBottom(){\r\n\t\t\t\tif(this.finish) return;\r\n\t\t\t\tthis.bottomLoadding = false;\r\n\t\t\t\tif(this.loading === false){\r\n\t\t\t\t\tthis.$emit('update:loading',true)\r\n\t\t\t\t\tthis.$emit(\"refresh\",'bottom')\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n.tm-pullBottom{\r\n\theight: 100%;\r\n\t.tm-pullBottom-sroll{\r\n\t\tposition: relative;\r\n\t\t.tm-pullBottom-top{\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\t.tm-pullBottom-top-icon{\r\n\t\t\t\ttransition: all 0.5s;\r\n\t\t\t\t&.pull{\r\n\t\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n}\n</style>\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-pullBottom.vue?vue&type=style&index=0&id=23db1152&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-pullBottom.vue?vue&type=style&index=0&id=23db1152&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775121\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}