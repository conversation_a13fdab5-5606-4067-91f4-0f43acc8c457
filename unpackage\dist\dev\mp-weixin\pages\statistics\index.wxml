<view class="{{['data-v-1961125f',$root.g0.tmVuetify.black?'black':'grey-lighten-5']}}" style="{{'min-height:'+(sys.windowHeight+'px')+';'}}"><tm-menubars vue-id="12c1ba87-1" color="primary" title="统计分析" shadow="{{0}}" class="data-v-1961125f" bind:__l="__l"></tm-menubars><view class="main-tabs-container data-v-1961125f"><tm-tabs vue-id="12c1ba87-2" fllowTheme="{{false}}" bg-color="amber" font-size="36" active-font-size="36" list="{{tabList}}" range-key="title" shadow="{{0}}" value="{{activeTabIndex}}" data-event-opts="{{[['^change',[['changeTab']]],['^input',[['__set_model',['','activeTabIndex','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-1961125f" bind:__l="__l"></tm-tabs></view><view class="{{['time-selector','data-v-1961125f',$root.g1.tmVuetify.black?'grey-darken-5 bk':'white']}}"><view class="flex flex-center px-32 py-24 data-v-1961125f"><block wx:if="{{activeTabIndex===0}}"><view class="time-picker-item data-v-1961125f"><tm-pickers-date vue-id="12c1ba87-3" showDetail="{{({year:true,month:false,day:false,hour:false,min:false,sec:false})}}" data-event-opts="{{[['^confirm',[['selectYear']]],['^input',[['changeValue']]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-1961125f" bind:__l="__l" vue-slots="{{['default']}}"><text class="text-primary text-size-m data-v-1961125f">{{"📅 "+currentYear+"年"}}</text></tm-pickers-date></view></block><block wx:if="{{activeTabIndex===1}}"><view class="time-picker-item data-v-1961125f"><tm-pickers-date vue-id="12c1ba87-4" showDetail="{{({year:true,month:true,day:false,hour:false,min:false,sec:false})}}" data-event-opts="{{[['^confirm',[['selectTime']]],['^input',[['changeValue']]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-1961125f" bind:__l="__l" vue-slots="{{['default']}}"><text class="text-primary text-size-m data-v-1961125f">{{"📅 "+time}}</text></tm-pickers-date></view></block></view></view><block wx:if="{{activeTabIndex===1}}"><view class="overview-cards mx-24 mt-24 data-v-1961125f"><tm-sheet vue-id="12c1ba87-5" bg-color="amber" shadow="{{2}}" round="12" padding="{{[24,20]}}" class="data-v-1961125f" bind:__l="__l" vue-slots="{{['default']}}"><view class="flex flex-between data-v-1961125f"><view class="overview-item data-v-1961125f"><view class="text-size-xs text-grey-darken-1 data-v-1961125f">总收入</view><view class="text-size-l text-green font-weight-bold mt-8 data-v-1961125f">{{'¥'+$root.g2+''}}</view></view><view class="overview-item data-v-1961125f"><view class="text-size-xs text-grey-darken-1 data-v-1961125f">总支出</view><view class="text-size-l text-red font-weight-bold mt-8 data-v-1961125f">{{'¥'+$root.g3+''}}</view></view><view class="overview-item data-v-1961125f"><view class="text-size-xs text-grey-darken-1 data-v-1961125f">净收入</view><view class="{{['text-size-l','font-weight-bold','mt-8','data-v-1961125f',netIncome>=0?'text-green':'text-red']}}">{{'¥'+$root.g4+''}}</view></view></view></tm-sheet></view></block><view class="charts-container mx-24 mt-16 data-v-1961125f"><block wx:if="{{activeTabIndex===0}}"><view class="data-v-1961125f"><view class="expense-type-tabs mb-24 data-v-1961125f"><tm-sheet vue-id="12c1ba87-6" fllowTheme="{{false}}" bg-color="amber" shadow="{{1}}" round="8" padding="{{[8,8]}}" class="data-v-1961125f" bind:__l="__l" vue-slots="{{['default']}}"><view class="flex data-v-1961125f"><block wx:for="{{expenseTypeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeExpenseType',[index]]]]]}}" class="{{['expense-type-item','data-v-1961125f',(currentExpenseType===index)?'active':'']}}" bindtap="__e"><text class="data-v-1961125f">{{item}}</text></view></block></view></tm-sheet></view><tm-sheet class="mb-24 data-v-1961125f" vue-id="12c1ba87-7" fllowTheme="{{false}}" bg-color="amber" shadow="{{2}}" round="12" padding="{{[24,20]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="chart-header data-v-1961125f"><view class="flex flex-between flex-middle data-v-1961125f"><view class="data-v-1961125f"><view class="text-size-m font-weight-bold data-v-1961125f">{{currentYear+"年"+expenseTypeList[currentExpenseType]+"趋势"}}</view><view class="text-size-xs text-grey-darken-1 mt-8 data-v-1961125f">全年12个月数据变化</view></view><block wx:if="{{yearlyStats.totalAmount>0}}"><view class="yearly-stats data-v-1961125f"><view class="stat-item data-v-1961125f"><view class="stat-label data-v-1961125f">年度总额</view><view class="{{['stat-value','data-v-1961125f',currentExpenseType===0?'text-red':'text-green']}}">{{'¥'+$root.g5+''}}</view></view></view></block></view></view><view class="charts-box data-v-1961125f" style="height:350px;"><block wx:if="{{$root.g6}}"><qiun-data-charts style="{{(!showCanvas?'display:none':'')}}" vue-id="{{('12c1ba87-8')+','+('12c1ba87-7')}}" type="line" chartData="{{yearlyTrendData}}" class="data-v-1961125f" bind:__l="__l"></qiun-data-charts></block><block wx:else><view class="empty-state data-v-1961125f"><view class="empty-icon data-v-1961125f">📈</view><view class="empty-text data-v-1961125f">{{"暂无"+currentYear+"年"+expenseTypeList[currentExpenseType]+"数据"}}</view></view></block></view></tm-sheet></view></block><block wx:if="{{activeTabIndex===1}}"><view class="data-v-1961125f"><view class="expense-type-tabs mb-24 data-v-1961125f"><tm-sheet vue-id="12c1ba87-9" bg-color="amber" shadow="{{1}}" round="8" padding="{{[8,8]}}" class="data-v-1961125f" bind:__l="__l" vue-slots="{{['default']}}"><view class="flex data-v-1961125f"><block wx:for="{{expenseTypeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeExpenseType',[index]]]]]}}" class="{{['expense-type-item','data-v-1961125f',(currentExpenseType===index)?'active':'']}}" bindtap="__e"><text class="data-v-1961125f">{{item}}</text></view></block></view></tm-sheet></view><tm-sheet class="mb-24 data-v-1961125f" vue-id="12c1ba87-10" bg-color="amber" shadow="{{2}}" round="12" padding="{{[24,20]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="chart-header data-v-1961125f"><view class="text-size-m font-weight-bold data-v-1961125f">{{expenseTypeList[currentExpenseType]+"类别排行"}}</view><view class="text-size-xs text-grey-darken-1 mt-8 data-v-1961125f">{{time+"月数据"}}</view></view><view class="charts-box data-v-1961125f" style="height:300px;"><block wx:if="{{$root.g7}}"><qiun-data-charts style="{{(!showCanvas?'display:none':'')}}" vue-id="{{('12c1ba87-11')+','+('12c1ba87-10')}}" type="column" chartData="{{categoryRankData}}" class="data-v-1961125f" bind:__l="__l"></qiun-data-charts></block><block wx:else><view class="empty-state data-v-1961125f"><view class="empty-icon data-v-1961125f">📊</view><view class="empty-text data-v-1961125f">{{"暂无"+expenseTypeList[currentExpenseType]+"数据"}}</view></view></block></view></tm-sheet><tm-sheet class="mb-24 data-v-1961125f" vue-id="12c1ba87-12" bg-color="amber" shadow="{{2}}" round="12" padding="{{[24,20]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="chart-header data-v-1961125f"><view class="text-size-m font-weight-bold data-v-1961125f">{{time+"月"+expenseTypeList[currentExpenseType]+"分布"}}</view><view class="text-size-xs text-grey-darken-1 mt-8 data-v-1961125f">{{month}}</view></view><view class="charts-box data-v-1961125f" style="height:300px;"><block wx:if="{{$root.g8}}"><qiun-data-charts style="{{(!showCanvas?'display:none':'')}}" vue-id="{{('12c1ba87-13')+','+('12c1ba87-12')}}" type="pie" chartData="{{monthData}}" class="data-v-1961125f" bind:__l="__l"></qiun-data-charts></block><block wx:else><view class="empty-state data-v-1961125f"><view class="empty-icon data-v-1961125f">📊</view><view class="empty-text data-v-1961125f">{{"暂无"+expenseTypeList[currentExpenseType]+"数据"}}</view></view></block></view></tm-sheet><tm-sheet class="mb-24 data-v-1961125f" vue-id="12c1ba87-14" bg-color="amber" shadow="{{2}}" round="12" padding="{{[24,20]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="chart-header data-v-1961125f"><view class="text-size-m font-weight-bold data-v-1961125f">{{$root.m0+expenseTypeList[currentExpenseType]+"分布"}}</view><view class="text-size-xs text-grey-darken-1 mt-8 data-v-1961125f">{{day}}</view></view><view class="charts-box data-v-1961125f" style="height:300px;"><block wx:if="{{$root.g9}}"><qiun-data-charts style="{{(!showCanvas?'display:none':'')}}" vue-id="{{('12c1ba87-15')+','+('12c1ba87-14')}}" type="pie" chartData="{{dayData}}" class="data-v-1961125f" bind:__l="__l"></qiun-data-charts></block><block wx:else><view class="empty-state data-v-1961125f"><view class="empty-icon data-v-1961125f">📊</view><view class="empty-text data-v-1961125f">{{"暂无"+expenseTypeList[currentExpenseType]+"数据"}}</view></view></block></view></tm-sheet></view></block></view></view>