{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-menubars/tm-menubars.vue?776b", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-menubars/tm-menubars.vue?0dee", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-menubars/tm-menubars.vue?38e0", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-menubars/tm-menubars.vue?a353", "uni-app:///tm-vuetify/components/tm-menubars/tm-menubars.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-menubars/tm-menubars.vue?81b7", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-menubars/tm-menubars.vue?e8e2"], "names": ["components", "tmIcons", "options", "multipleSlots", "name", "props", "black", "type", "default", "color", "fontColor", "theme", "flat", "transparent", "scrollTobg", "showback", "title", "homeUrl", "shadow", "fllowTheme", "data", "pageUrl", "nowScrollTop", "isHome", "tabHeight", "computed", "black_tmeme", "color_tmeme", "fontColorTheme", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ColorThemeName", "widths", "jnwd", "btns", "created", "sysbarheight", "mounted", "uni", "nopage", "methods"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,6pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC2DnrB;EACAA;IACAC;EACA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA,gGACAR;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAS;MACA;MACA;QACA;UACA;UACA;QACA;MACA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;;MAEA;MACA;MACAC;MAEA;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IAEAC;IAEA;EAEA;EACAC;IAAA;IAEAC;MACA;IACA;IACA;IACA;IACA;IACA;MACAC;IACA;IACA;MACA;IACA;IACA;MAKA;IAEA;EACA;EACAC;AACA;;;;;;;;;;;;;;ACtNA;AAAA;AAAA;AAAA;AAA8wC,CAAgB,osCAAG,EAAC,C;;;;;;;;;;;ACAlyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-menubars/tm-menubars.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-menubars.vue?vue&type=template&id=2a0d9096&scoped=true&\"\nvar renderjs\nimport script from \"./tm-menubars.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-menubars.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-menubars.vue?vue&type=style&index=0&id=2a0d9096&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2a0d9096\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-menubars/tm-menubars.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-menubars.vue?vue&type=template&id=2a0d9096&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  _vm.$initSSP()\n  if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n    _vm.$setSSP(\"left\", {\n      data: {\n        style: _vm.widths,\n        isTransparent: _vm.isTransparent,\n        title: _vm.title,\n      },\n    })\n    _vm.$setSSP(\"default\", {\n      data: {\n        style: _vm.widths,\n        isTransparent: _vm.isTransparent,\n        title: _vm.title,\n      },\n    })\n    _vm.$setSSP(\"right\", {\n      data: {\n        style: _vm.widths,\n        isTransparent: _vm.isTransparent,\n        title: _vm.title,\n      },\n    })\n  }\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-menubars.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-menubars.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view @click.stop=\"\" class=\"tm-menubars fulled \">\r\n\t\t<view v-if=\"!transparent\" :style=\"{ height: tabHeight }\"></view>\r\n\t\t<view v-if=\"!transparent\" :style=\"{ height: '45px' }\"></view>\r\n\t\t<view class=\"body  fulled\"  :class=\"[\r\n\t\t\t\tblack_tmeme ? 'bk grey-darken-5 ' : '',\r\n\t\t\t\t'shadow-' + color_tmeme + `-${shadow} `,\r\n\t\t\t\ttransparent ? (isTransparent ? 'transparent flat ' + `text-${fontColorTheme}` : color_tmeme) : color_tmeme,\r\n\t\t\t\tflat ? 'flat' : ''\r\n\t\t\t]\">\r\n\t\t\t<view :style=\"{ height: tabHeight }\"></view>\r\n\t\t\t<view class=\"body_wk flex-between\">\r\n\t\t\t\t<view class=\"left flex-start\">\r\n\t\t\t\t\t<block v-if=\"showback\">\r\n\t\t\t\t\t\t<view v-if=\"pageUrl && isHome == false\" class=\"home-btn mr-20 text flex-center flex-shrink\"\r\n\t\t\t\t\t\t\t:class=\"[color_tmeme,black_tmeme ? 'outlined bk' : '']\">\r\n\t\t\t\t\t\t\t<navigator :url=\"pageUrl\" open-type=\"reLaunch\" class=\"flex-center\">\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-home\" :style=\"{ fontSize: '32rpx' }\"></text>\r\n\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<navigator v-if=\"!pageUrl\" open-type=\"navigateBack\" class=\"flex-center px-24 flex-shrink fulled-height\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont icon-angle-left\" :class=\"[`text-${fontColorTheme}`]\" :style=\"{ fontSize: '28rpx' }\"></text>\r\n\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<slot name=\"left\" :data=\"{ style: widths, isTransparent: isTransparent, title: title }\"></slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"center flex-center text-size-g text-overflow text-align-center\" :class=\"[`text-${fontColorTheme}`]\">\r\n\t\t\t\t\t<slot name=\"default\" :data=\"{ style: widths, isTransparent: isTransparent, title: title }\">\r\n\t\t\t\t\t\t{{ title }}\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right flex-end\" :style=\"{ width: widths.btns }\">\r\n\t\t\t\t\t<slot name=\"right\" :data=\"{ style: widths, isTransparent: isTransparent, title: title }\"></slot>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\t\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * 菜单工具栏\r\n\t * @property {Boolean} black = [true|false] 默认:false,暗黑模式。\r\n\t * @property {String|Array} color = [primary] 默认:primary,主题颜色名称如果为array,则为渐变色格式第一个方向：top,bottom,right,left[top,color1,color2]，如果为string时，可以为rgba,rgb,#fff或者颜色主题名称如：purple-darken-4\r\n\t * @property {String} theme = [black|white] 默认:custom,只有在transparent:true时，black:表示黑色模式，文字变白。white文字变黑。\r\n\t * @property {String} font-color = [] 默认:null,文字颜色,默认不需要提供根据主题色自动推断。一旦赋值，自带的theme失效。使用用户颜色类。\r\n\t * @property {String} home-url = [] 默认:'/pages/index/index',应用的首页地址。当从其它页面以reLaunch进入非首页时会自动显示首页按钮。\r\n\t * @property {Boolean} flat = [true|false] 默认:false,去除投影，边线\r\n\t * @property {Boolean} transparent = [true|false] 默认:false,开启透明顶部。下拉时会变成自定义的背景色。\r\n\t * @property {Number} scroll-tobg = [true|false] 默认:0,当值大于0即开启透明背景。下拉时达到设定的值，即显示自定义的背景和文字色。\r\n\t * @property {Number | String} width = [] 默认:0,宽度，数字，或者百度比。数字的单位是upx\r\n\t * @property {Boolean} showback = [true|false] 默认:true,是否显示左边的返回和首页按钮。\r\n\t * @property {String} title = [] 默认:标题, 中间标题文字。\r\n\t * @example <tm-menubars  color=\"white\" :showback=\"false\"></tm-menubars>\r\n\t *\r\n\t */\r\n\r\n\timport tmIcons from '@/tm-vuetify/components/tm-icons/tm-icons.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttmIcons\r\n\t\t},\r\n\t\toptions:{\r\n\t\t\tmultipleSlots: true\r\n\t\t},\r\n\t\tname: 'tm-menubars',\r\n\t\tprops: {\r\n\t\t\t// 是否开启暗黑模式\r\n\t\t\tblack: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\t// 主题颜色名称如果为array,则为渐变色格式第一个方向：top,bottom,right,left[top,color1,color2]\r\n\t\t\t// 如果为string时，可以为rgba,rgb,#fff或者颜色主题名称如：purple-darken-4\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String | Array,\r\n\t\t\t\tdefault: 'primary'\r\n\t\t\t},\r\n\t\t\tfontColor:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:''\r\n\t\t\t},\r\n\t\t\t// custom为自定导航样式 。black标题文字为白。white标题文字为黑。它表示的是所处背景的模式。\r\n\t\t\ttheme: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'custom' //'black'|'white'|'custom'\r\n\t\t\t},\r\n\t\t\t// 几乎所有组件都有flat选项，去除投影，边线。\r\n\t\t\tflat: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 是否开启顶部透明模式。\r\n\t\t\ttransparent: {\r\n\t\t\t\ttype: String | Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 当值大于0即开启透明背景。下拉时达到设定的值，即显示自定义的背景和文字色。\r\n\t\t\tscrollTobg: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 70\r\n\t\t\t},\r\n\t\t\t// 是否显示左边的返回和首页按钮。\r\n\t\t\tshowback: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 中间标题文字。\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '标题'\r\n\t\t\t},\r\n\t\t\t// 首页页面地址。当前访问子页面时，将会显示首页按钮。\r\n\t\t\thomeUrl: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '/pages/index/index'\r\n\t\t\t},\r\n\t\t\tshadow: {\r\n\t\t\t\ttype: String | Number,\r\n\t\t\t\tdefault: 3\r\n\t\t\t},\r\n\t\t\t// 跟随主题色的改变而改变。\r\n\t\t\tfllowTheme: {\r\n\t\t\t\ttype: Boolean | String,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpageUrl: '',\r\n\t\t\t\tnowScrollTop: 0,\r\n\t\t\t\tisHome: false,\r\n\t\t\t\ttabHeight: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tblack_tmeme: function() {\r\n\t\t\t\tif (this.black !== null) return this.black;\r\n\t\t\t\treturn this.$tm.vx.state().tmVuetify.black;\r\n\t\t\t},\r\n\t\t\tcolor_tmeme: function() {\r\n\t\t\t\tif (this.$tm.vx.state().tmVuetify.color !== null && this.$tm.vx.state().tmVuetify.color && this\r\n\t\t\t\t\t.fllowTheme) {\r\n\t\t\t\t\treturn this.$tm.vx.state().tmVuetify.color;\r\n\t\t\t\t}\r\n\t\t\t\tif(this.transparent){\r\n\t\t\t\t\tif(this.isTransparent) return ''\r\n\t\t\t\t}\r\n\t\t\t\treturn this.color;\r\n\t\t\t},\r\n\t\t\tfontColorTheme:function(){\r\n\t\t\t\tif(this.theme == 'custom') return this.fontColor;\r\n\t\t\t\tif(this.transparent){\r\n\t\t\t\t\tif(this.isTransparent){\r\n\t\t\t\t\t\tif(this.theme == 'black') return 'white';\r\n\t\t\t\t\t\tif(this.theme == 'white') return 'black';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\treturn this.fontColor;\r\n\t\t\t},\r\n\t\t\t// 当页面在滚动时返回当前是透明还是不透明背景。\r\n\t\t\tisTransparent: function() {\r\n\t\t\t\treturn this.nowScrollTop < this.scrollTobg;\r\n\t\t\t},\r\n\t\t\tColorThemeName:function(){\r\n\t\t\t\treturn this.color;\r\n\t\t\t},\r\n\t\t\twidths: function() {\r\n\t\t\t\tlet jnwd = 0; //小程序有的胶囊宽度.\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\t// 胶囊的大小。\r\n\t\t\t\tlet mw = uni.getMenuButtonBoundingClientRect();\r\n\t\t\t\tjnwd = mw.width;\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn uni.$tm.objToString({\r\n\t\t\t\t\tbtns: jnwd + 'px',\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tlet sysinfo = uni.getSystemInfoSync();\r\n\t\t\tlet sysbarheight = 0;\r\n\t\t\t// #ifdef MP || APP-PLUS || APP-VUE\r\n\t\t\tsysbarheight = sysinfo.statusBarHeight;\r\n\t\t\t// #endif\r\n\t\t\tthis.tabHeight = sysbarheight + 'px';\r\n\t\t\t\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\r\n\t\t\tuni.$on('onPageScroll', e => {\r\n\t\t\t\tthis.nowScrollTop = e.scrollTop;\r\n\t\t\t});\r\n\t\t\t// 检查页面栈\r\n\t\t\tlet pages = getCurrentPages();\r\n\t\t\tlet nopage = pages[pages.length - 1].route;\r\n\t\t\tif (nopage[0] != '/') {\r\n\t\t\t\tnopage = '/' + nopage;\r\n\t\t\t}\r\n\t\t\tif (nopage == this.homeUrl) {\r\n\t\t\t\tthis.isHome = true;\r\n\t\t\t}\r\n\t\t\tif (pages.length == 1 && typeof pages[0].route !== 'undefined') {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tthis.pageUrl = '/';\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tthis.pageUrl = this.homeUrl;\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {}\r\n\t};\r\n\t//\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.tm-menubars {\r\n\t\t.body {\r\n\t\t\tposition: fixed;\r\n\t\t\tz-index: 450;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\r\n\t\t\t&.transparent {\r\n\t\t\t\tbackground: none !important;\r\n\t\t\t\ttransition: all 0.6s;\r\n\t\t\t}\r\n\r\n\t\t\t.body_wk {\r\n\t\t\t\theight: 45px;\r\n\t\t\t\t// opacity: 0.9;\r\n\r\n\t\t\t\t.left {\r\n\t\t\t\t\tmax-width: 74px;\r\n\t\t\t\t\tmin-width: 74px;\r\n\r\n\t\t\t\t\t.home-btn {\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\r\n\t\t\t\t\t\theight: 30px;\r\n\t\t\t\t\t\twidth: 30px;\r\n\t\t\t\t\t\tline-height: 30px;\r\n\t\t\t\t\t\tmargin-left: 24upx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.right {\r\n\t\t\t\t\tmax-width: 74px;\r\n\t\t\t\t\tmin-width: 74px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.center {\r\n\t\t\t\t\twidth: calc(100% - 148px);\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-menubars.vue?vue&type=style&index=0&id=2a0d9096&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-menubars.vue?vue&type=style&index=0&id=2a0d9096&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775140\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}