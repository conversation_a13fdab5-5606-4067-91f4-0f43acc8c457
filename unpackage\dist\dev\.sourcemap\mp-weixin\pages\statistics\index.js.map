{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/source/bill-view/pages/statistics/index.vue?0d86", "webpack:///D:/source/bill-view/pages/statistics/index.vue?27fd", "webpack:///D:/source/bill-view/pages/statistics/index.vue?ac18", "webpack:///D:/source/bill-view/pages/statistics/index.vue?3d0e", "uni-app:///pages/statistics/index.vue", "webpack:///D:/source/bill-view/pages/statistics/index.vue?b8f2", "webpack:///D:/source/bill-view/pages/statistics/index.vue?41ea", "webpack:///D:/source/bill-view/pages/statistics/index.vue?72e2", "webpack:///D:/source/bill-view/pages/statistics/index.vue?1006"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tmTabs", "tmMenubars", "tmSheet", "tmPickersDate", "data", "time", "tabList", "title", "activeTabIndex", "expenseTypeList", "currentExpenseType", "list", "activeIndex", "show", "word", "type", "er", "times", "types", "month", "day", "charts", "monthData", "dayData", "categoryRankData", "categories", "series", "yearlyTrendData", "yearlyStats", "totalAmount", "totalCount", "avgAmount", "maxAmount", "minAmount", "trendAnalysis", "currentYear", "now", "Showhiddenunits", "showCanvas", "totalIncome", "totalExpense", "netIncome", "loading", "watch", "created", "onLoad", "onShow", "mounted", "methods", "loadDataForCurrentTab", "formatDate", "updateOverviewData", "uid", "updateNetIncome", "generateMockYearlyData", "values", "text", "left", "tooltip", "trigger", "formatter", "legend", "bottom", "grid", "right", "top", "containLabel", "xAxis", "boundaryGap", "axisLabel", "fontSize", "yAxis", "name", "smooth", "symbol", "symbolSize", "lineStyle", "width", "color", "areaStyle", "opacity", "itemStyle", "borderWidth", "borderColor", "loadYearlyTrendData", "year", "console", "changeValue", "loadDayData", "subtext", "orient", "radius", "center", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "label", "loadMonthData", "generateCategoryRankData", "loadType", "selectType", "<PERSON><PERSON><PERSON>", "open", "selectTime", "selectYear", "getCurrentTime", "changeTab", "changeExpenseType", "change", "confirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACc;AACwB;;;AAG1F;AAC2K;AAC3K,gBAAgB,qLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qXAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,upBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8M9pB;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACA;MACAC;QAAAC;MAAA;QAAAA;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QAAA;QACAC;QACAC;MACA;MACA;MACAC;QACAF;QACAC;MACA;MACA;MACAE;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAN;MACA;IACA;EACA;EACAO;IACA;EAIA;EACAC;IAEA;EAGA;EACAC;IACA;IACA;EACA;EACAC;IAAA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;UACAC;UACApC;UAAA;UACAX;QACA;UACA;UACA;QACA;;QAEA;QACA;UACA+C;UACApC;UAAA;UACAX;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAgD;MACA;IACA;IAEA;IACAC;MACA;MACA,kDACA;MACA;;MAEA;MACA;QACA;QACA;QACAC;MACA;MAEA;QACAhD;UACAiD;UACAC;QACA;QACAC;UACAC;UACAC;YACA;cACA;cACA;cACA;YACA;YACA;UACA;QACA;QACAC;UACAzD;UACA0D;QACA;QACAC;UACAN;UACAO;UACAF;UACAG;UACAC;QACA;QACAC;UACApD;UACAqD;UACAhE;UACAiE;YACAC;UACA;QACA;QACAC;UACAxD;UACAyD;UACAH;YACAT;cACA;gBACA;cACA;gBACA;cACA;gBACA;cACA;YACA;UACA;QACA;QACAlC;UACA8C;UACAzD;UACAX;UACAqE;UACAC;UACAC;UACAC;YACAC;YACAC;UACA;UACAC;YACAC;YACAF;UACA;UACAG;YACAH;YACAI;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UACAhC;UACAiC;UACArE;UACA;UACA;QACA;UACAsE;;UAEA;UACA;;UAEA;UACA,kDACA;UACA;;UAEA;UACA;UACA;YACA/B;UACA;UAEA+B;;UAEA;UACA;YACAzD;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;UAEAoD;UAEA;YACA/E;cACAiD;cACAC;YACA;YACAC;cACAC;cACAC;gBACA;kBACA;kBACA;kBACA;gBACA;gBACA;cACA;YACA;YACAC;cACAzD;cACA0D;YACA;YACAC;cACAN;cACAO;cACAF;cACAG;cACAC;YACA;YACAC;cACApD;cACAqD;cACAhE;cACAiE;gBACAC;cACA;YACA;YACAC;cACAxD;cACAyD;cACAH;gBACAT;kBACA;oBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;cACA;YACA;YACAlC;cACA8C;cACAzD;cACAX;cACAqE;cACAC;cACAC;cACAC;gBACAC;gBACAC;cACA;cACAC;gBACAC;gBACAF;cACA;cACAG;gBACAH;gBACAI;gBACAC;cACA;YACA;UACA;QACA;UACAG;UACAA;UACA;UACA;QACA;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MAAA;MACA;QACA;UACApC;UACApC;UACAX;QACA;UACA;UACA;YACAE;cACAkF;cACAhC;YACA;YACAC;cACAC;cACAC;YACA;YACAC;cACA6B;cACA5B;cACAL;YACA;YACA/B;cACA8C;cACAzD;cACA4E;cACAC;cACAxF;cACAyF;gBACAZ;kBACAa;kBACAC;kBACAC;gBACA;cACA;cACAC;gBACApF;gBACA+C;cACA;YACA;UACA;UACA;UACA;UACA;QACA;UACA0B;UACA;UACA;YACA5D;UACA;UACA;QACA;MACA;IACA;IACAwE;MAAA;MACA;QACA;UACA9C;UACApC;UACAX;QACA;UACA;UACA;YACAE;cACAkF;cACAhC;YACA;YACAC;cACAC;cACAC;YACA;YACAC;cACA6B;cACA5B;cACAL;YACA;YACA/B;cACA8C;cACAzD;cACA4E;cACAC;cACAxF;cACAyF;gBACAZ;kBACAa;kBACAC;kBACAC;gBACA;cACA;cACAC;gBACApF;gBACA+C;cACA;YACA;UACA;UAEA;UACA;UACA;;UAEA;UACA;QACA;UACA0B;UACA;UACA;YACA5D;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAyE;MACA;MACA;QACA;UACA1E;UACAC;QACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MAEA;QACA;UACAD;UACAC;QACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;MAEA;QAAA;MAAA;MACA;QAAA;MAAA;MAEA;QACAD;QACAC;UACA8C;UACApE;UACA0E;QACA;;QACAP;UACAnE;QACA;QACA+D;UACAK;QACA;MACA;IACA;IACA4B;MAAA;MACA;MACA;MACA;MACA;MACA;QAEA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACArB;MACA;IACA;IAEA;IACAsB;MACA;MACA;MACA;;MAEAtB;;MAEA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAuB;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA;EAGA;AACA;AAAA,2B;;;;;;;;;;;;;AClzBA;AAAA;AAAA;AAAA;AAAqtC,CAAgB,sqCAAG,EAAC,C;;;;;;;;;;;ACAzuC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA6uC,CAAgB,8rCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/statistics/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/statistics/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1961125f&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=1961125f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1961125f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/statistics/index.vue\"\nexport default component.exports", "export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1961125f&scoped=true&\"", "var components\ntry {\n  components = {\n    qiunDataCharts: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts\" */ \"@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$tm.vx.state()\n  var g1 = _vm.$tm.vx.state()\n  var g2 = _vm.activeTabIndex === 1 ? _vm.totalIncome.toFixed(2) : null\n  var g3 = _vm.activeTabIndex === 1 ? _vm.totalExpense.toFixed(2) : null\n  var g4 = _vm.activeTabIndex === 1 ? _vm.netIncome.toFixed(2) : null\n  var g5 =\n    _vm.activeTabIndex === 0 && _vm.yearlyStats.totalAmount > 0\n      ? _vm.yearlyStats.totalAmount.toFixed(2)\n      : null\n  var g6 =\n    _vm.activeTabIndex === 0\n      ? _vm.yearlyTrendData.series &&\n        _vm.yearlyTrendData.series.length > 0 &&\n        _vm.yearlyTrendData.series[0].data &&\n        _vm.yearlyTrendData.series[0].data.length > 0\n      : null\n  var g7 =\n    _vm.activeTabIndex === 1\n      ? _vm.categoryRankData.series &&\n        _vm.categoryRankData.series.length > 0 &&\n        _vm.categoryRankData.series[0].data &&\n        _vm.categoryRankData.series[0].data.length > 0\n      : null\n  var g8 =\n    _vm.activeTabIndex === 1\n      ? _vm.monthData.series &&\n        _vm.monthData.series.length > 0 &&\n        _vm.monthData.series[0].data &&\n        _vm.monthData.series[0].data.length > 0\n      : null\n  var m0 = _vm.activeTabIndex === 1 ? _vm.formatDate(_vm.now) : null\n  var g9 =\n    _vm.activeTabIndex === 1\n      ? _vm.dayData.series &&\n        _vm.dayData.series.length > 0 &&\n        _vm.dayData.series[0].data &&\n        _vm.dayData.series[0].data.length > 0\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n        m0: m0,\n        g9: g9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :style=\"{ minHeight: sys.windowHeight + 'px' }\"\r\n\t\t:class=\"[$tm.vx.state().tmVuetify.black ? 'black' : 'grey-lighten-5']\">\r\n\t\t<tm-menubars color=\"primary\" title=\"统计分析\" :shadow=\"0\">\r\n\t\t</tm-menubars>\r\n\r\n\t\t<!-- 主要切换标签 - 年度/月度 -->\r\n\t\t<view class=\"main-tabs-container\">\r\n\t\t\t<tm-tabs :fllowTheme=\"false\" bg-color=\"amber\"\r\n\t\t\t\tfont-size=\"36\" active-font-size=\"36\" @change=\"changeTab\"\r\n\t\t\t\tv-model=\"activeTabIndex\" :list=\"tabList\" range-key=\"title\" :shadow=\"0\">\r\n\t\t\t</tm-tabs>\r\n\t\t</view>\r\n\r\n\t\t<!-- 时间选择器 -->\r\n\t\t<view class=\"time-selector\"\r\n\t\t\t:class=\"[$tm.vx.state().tmVuetify.black ? 'grey-darken-5 bk' : 'white']\">\r\n\t\t\t<view class=\"flex flex-center px-32 py-24\">\r\n\t\t\t\t<!-- 年度选择器 - 年度tab时显示 -->\r\n\t\t\t\t<view v-if=\"activeTabIndex === 0\" class=\"time-picker-item\">\r\n\t\t\t\t\t<tm-pickersDate @confirm=\"selectYear\" @input=\"changeValue\"\r\n\t\t\t\t\t\t:showDetail=\"{year:true,month:false,day:false,hour:false,min:false,sec:false}\">\r\n\t\t\t\t\t\t<text class=\"text-primary text-size-m\">📅 {{ currentYear }}年</text>\r\n\t\t\t\t\t</tm-pickersDate>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 月度选择器 - 月度tab时显示 -->\r\n\t\t\t\t<view v-if=\"activeTabIndex === 1\" class=\"time-picker-item\">\r\n\t\t\t\t\t<tm-pickersDate @confirm=\"selectTime\" @input=\"changeValue\"\r\n\t\t\t\t\t\t:showDetail=\"{year:true,month:true,day:false,hour:false,min:false,sec:false}\">\r\n\t\t\t\t\t\t<text class=\"text-primary text-size-m\">📅 {{ time }}</text>\r\n\t\t\t\t\t</tm-pickersDate>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 收支概览卡片 - 仅在月度tab显示 -->\r\n\t\t<view v-if=\"activeTabIndex === 1\" class=\"overview-cards mx-24 mt-24\">\r\n\t\t\t<tm-sheet bg-color=\"amber\" :shadow=\"2\" round=\"12\" :padding=\"[24,20]\">\r\n\t\t\t\t<view class=\"flex flex-between\">\r\n\t\t\t\t\t<view class=\"overview-item\">\r\n\t\t\t\t\t\t<view class=\"text-size-xs text-grey-darken-1\">总收入</view>\r\n\t\t\t\t\t\t<view class=\"text-size-l text-green font-weight-bold mt-8\">\r\n\t\t\t\t\t\t\t¥{{ totalIncome.toFixed(2) }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"overview-item\">\r\n\t\t\t\t\t\t<view class=\"text-size-xs text-grey-darken-1\">总支出</view>\r\n\t\t\t\t\t\t<view class=\"text-size-l text-red font-weight-bold mt-8\">\r\n\t\t\t\t\t\t\t¥{{ totalExpense.toFixed(2) }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"overview-item\">\r\n\t\t\t\t\t\t<view class=\"text-size-xs text-grey-darken-1\">净收入</view>\r\n\t\t\t\t\t\t<view class=\"text-size-l font-weight-bold mt-8\"\r\n\t\t\t\t\t\t\t:class=\"netIncome >= 0 ? 'text-green' : 'text-red'\">\r\n\t\t\t\t\t\t\t¥{{ netIncome.toFixed(2) }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</tm-sheet>\r\n\t\t</view>\r\n\r\n\t\t<!-- 图表内容区域 -->\r\n\t\t<view class=\"charts-container mx-24 mt-16\">\r\n\t\t\t<!-- 年度视图 -->\r\n\t\t\t<view v-if=\"activeTabIndex === 0\">\r\n\t\t\t\t<!-- 收支类型切换 -->\r\n\t\t\t\t<view class=\"expense-type-tabs mb-24\">\r\n\t\t\t\t\t<tm-sheet :fllowTheme=\"false\" bg-color=\"amber\" :shadow=\"1\" round=\"8\" :padding=\"[8,8]\">\r\n\t\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in expenseTypeList\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\tclass=\"expense-type-item\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'active': currentExpenseType === index }\"\r\n\t\t\t\t\t\t\t\t@click=\"changeExpenseType(index)\">\r\n\t\t\t\t\t\t\t\t<text>{{ item }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</tm-sheet>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 年度趋势图 -->\r\n\t\t\t\t<tm-sheet :fllowTheme=\"false\" bg-color=\"amber\" :shadow=\"2\" round=\"12\" :padding=\"[24,20]\" class=\"mb-24\">\r\n\t\t\t\t\t<view class=\"chart-header\">\r\n\t\t\t\t\t\t<view class=\"flex flex-between flex-middle\">\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-size-m font-weight-bold\">{{ currentYear }}年{{ expenseTypeList[currentExpenseType] }}趋势</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-size-xs text-grey-darken-1 mt-8\">全年12个月数据变化</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"yearly-stats\" v-if=\"yearlyStats.totalAmount > 0\">\r\n\t\t\t\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"stat-label\">年度总额</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"stat-value\" :class=\"currentExpenseType === 0 ? 'text-red' : 'text-green'\">\r\n\t\t\t\t\t\t\t\t\t\t¥{{ yearlyStats.totalAmount.toFixed(2) }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"charts-box\" style=\"height: 350px;\">\r\n\t\t\t\t\t\t<!-- 当有数据时显示趋势图 -->\r\n\t\t\t\t\t\t<qiun-data-charts\r\n\t\t\t\t\t\t\tv-if=\"yearlyTrendData.series && yearlyTrendData.series.length > 0 && yearlyTrendData.series[0].data && yearlyTrendData.series[0].data.length > 0\"\r\n\t\t\t\t\t\t\ttype=\"line\"\r\n\t\t\t\t\t\t\t:chartData=\"yearlyTrendData\"\r\n\t\t\t\t\t\t\t:style=\"!showCanvas?'display:none':''\" />\r\n\t\t\t\t\t\t<!-- 当没有数据时显示空状态 -->\r\n\t\t\t\t\t\t<view v-else class=\"empty-state\">\r\n\t\t\t\t\t\t\t<view class=\"empty-icon\">📈</view>\r\n\t\t\t\t\t\t\t<view class=\"empty-text\">暂无{{ currentYear }}年{{ expenseTypeList[currentExpenseType] }}数据</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tm-sheet>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 月度视图 -->\r\n\t\t\t<view v-if=\"activeTabIndex === 1\">\r\n\t\t\t\t<!-- 收支类型切换 -->\r\n\t\t\t\t<view class=\"expense-type-tabs mb-24\">\r\n\t\t\t\t\t<tm-sheet bg-color=\"amber\" :shadow=\"1\" round=\"8\" :padding=\"[8,8]\">\r\n\t\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in expenseTypeList\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\tclass=\"expense-type-item\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'active': currentExpenseType === index }\"\r\n\t\t\t\t\t\t\t\t@click=\"changeExpenseType(index)\">\r\n\t\t\t\t\t\t\t\t<text>{{ item }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</tm-sheet>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 类别排行柱状图 -->\r\n\t\t\t\t<tm-sheet bg-color=\"amber\" :shadow=\"2\" round=\"12\" :padding=\"[24,20]\" class=\"mb-24\">\r\n\t\t\t\t\t<view class=\"chart-header\">\r\n\t\t\t\t\t\t<view class=\"text-size-m font-weight-bold\">{{ expenseTypeList[currentExpenseType] }}类别排行</view>\r\n\t\t\t\t\t\t<view class=\"text-size-xs text-grey-darken-1 mt-8\">{{ time }}月数据</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"charts-box\" style=\"height: 300px;\">\r\n\t\t\t\t\t\t<!-- 当有数据时显示柱状图 -->\r\n\t\t\t\t\t\t<qiun-data-charts\r\n\t\t\t\t\t\t\tv-if=\"categoryRankData.series && categoryRankData.series.length > 0 && categoryRankData.series[0].data && categoryRankData.series[0].data.length > 0\"\r\n\t\t\t\t\t\t\ttype=\"column\"\r\n\t\t\t\t\t\t\t:chartData=\"categoryRankData\"\r\n\t\t\t\t\t\t\t:style=\"!showCanvas?'display:none':''\" />\r\n\t\t\t\t\t\t<!-- 当没有数据时显示空状态 -->\r\n\t\t\t\t\t\t<view v-else class=\"empty-state\">\r\n\t\t\t\t\t\t\t<view class=\"empty-icon\">📊</view>\r\n\t\t\t\t\t\t\t<view class=\"empty-text\">暂无{{ expenseTypeList[currentExpenseType] }}数据</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tm-sheet>\r\n\r\n\t\t\t\t<!-- 月度饼图 -->\r\n\t\t\t\t<tm-sheet bg-color=\"amber\" :shadow=\"2\" round=\"12\" :padding=\"[24,20]\" class=\"mb-24\">\r\n\t\t\t\t\t<view class=\"chart-header\">\r\n\t\t\t\t\t\t<view class=\"text-size-m font-weight-bold\">{{ time }}月{{ expenseTypeList[currentExpenseType] }}分布</view>\r\n\t\t\t\t\t\t<view class=\"text-size-xs text-grey-darken-1 mt-8\">{{ month }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"charts-box\" style=\"height: 300px;\">\r\n\t\t\t\t\t\t<!-- 当有数据时显示饼图 -->\r\n\t\t\t\t\t\t<qiun-data-charts\r\n\t\t\t\t\t\t\tv-if=\"monthData.series && monthData.series.length > 0 && monthData.series[0].data && monthData.series[0].data.length > 0\"\r\n\t\t\t\t\t\t\ttype=\"pie\"\r\n\t\t\t\t\t\t\t:chartData=\"monthData\"\r\n\t\t\t\t\t\t\t:style=\"!showCanvas?'display:none':''\" />\r\n\t\t\t\t\t\t<!-- 当没有数据时显示空状态 -->\r\n\t\t\t\t\t\t<view v-else class=\"empty-state\">\r\n\t\t\t\t\t\t\t<view class=\"empty-icon\">📊</view>\r\n\t\t\t\t\t\t\t<view class=\"empty-text\">暂无{{ expenseTypeList[currentExpenseType] }}数据</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tm-sheet>\r\n\r\n\t\t\t\t<!-- 日度饼图 -->\r\n\t\t\t\t<tm-sheet bg-color=\"amber\" :shadow=\"2\" round=\"12\" :padding=\"[24,20]\" class=\"mb-24\">\r\n\t\t\t\t\t<view class=\"chart-header\">\r\n\t\t\t\t\t\t<view class=\"text-size-m font-weight-bold\">{{ formatDate(now) }}{{ expenseTypeList[currentExpenseType] }}分布</view>\r\n\t\t\t\t\t\t<view class=\"text-size-xs text-grey-darken-1 mt-8\">{{ day }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"charts-box\" style=\"height: 300px;\">\r\n\t\t\t\t\t\t<!-- 当有数据时显示饼图 -->\r\n\t\t\t\t\t\t<qiun-data-charts\r\n\t\t\t\t\t\t\tv-if=\"dayData.series && dayData.series.length > 0 && dayData.series[0].data && dayData.series[0].data.length > 0\"\r\n\t\t\t\t\t\t\ttype=\"pie\"\r\n\t\t\t\t\t\t\t:chartData=\"dayData\"\r\n\t\t\t\t\t\t\t:style=\"!showCanvas?'display:none':''\" />\r\n\t\t\t\t\t\t<!-- 当没有数据时显示空状态 -->\r\n\t\t\t\t\t\t<view v-else class=\"empty-state\">\r\n\t\t\t\t\t\t\t<view class=\"empty-icon\">📊</view>\r\n\t\t\t\t\t\t\t<view class=\"empty-text\">暂无{{ expenseTypeList[currentExpenseType] }}数据</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tm-sheet>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport tmMenubars from '@/tm-vuetify/components/tm-menubars/tm-menubars.vue';\r\n\timport tmTabs from \"@/tm-vuetify/components/tm-tabs/tm-tabs.vue\"\r\n\timport tmSheet from \"@/tm-vuetify/components/tm-sheet/tm-sheet.vue\"\r\n\timport tmPickersDate from \"@/tm-vuetify/components/tm-pickersDate/tm-pickersDate.vue\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttmTabs,\r\n\t\t\ttmMenubars,\r\n\t\t\ttmSheet,\r\n\t\t\ttmPickersDate,\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttime: '',\r\n\t\t\t\t// 主要tab列表\r\n\t\t\t\ttabList: [{title:'年度'}, {title:'月度'}],\r\n\t\t\t\tactiveTabIndex: 0, // 当前激活的主tab\r\n\t\t\t\t// 收支类型列表\r\n\t\t\t\texpenseTypeList: ['支出', '收入'],\r\n\t\t\t\tcurrentExpenseType: 0, // 当前选择的收支类型\r\n\t\t\t\t// 保留原有的数据结构以兼容现有代码\r\n\t\t\t\tlist: ['支出', '收入'],\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\tshow: true,\r\n\t\t\t\tword: '',\r\n\t\t\t\ttype: {},\r\n\t\t\t\ter: 0,\r\n\t\t\t\ttimes: '',\r\n\t\t\t\ttypes: [],\r\n\t\t\t\tmonth: '',\r\n\t\t\t\tday: '',\r\n\t\t\t\tcharts: {},\r\n\t\t\t\tmonthData: {},\r\n\t\t\t\tdayData: {},\r\n\t\t\t\tcategoryRankData: { // 新增：类别排行柱状图数据\r\n\t\t\t\t\tcategories: [],\r\n\t\t\t\t\tseries: []\r\n\t\t\t\t},\r\n\t\t\t\t// 新增：年度趋势图数据\r\n\t\t\t\tyearlyTrendData: {\r\n\t\t\t\t\tcategories: [],\r\n\t\t\t\t\tseries: []\r\n\t\t\t\t},\r\n\t\t\t\t// 新增：年度统计数据\r\n\t\t\t\tyearlyStats: {\r\n\t\t\t\t\ttotalAmount: 0,\r\n\t\t\t\t\ttotalCount: 0,\r\n\t\t\t\t\tavgAmount: 0,\r\n\t\t\t\t\tmaxAmount: 0,\r\n\t\t\t\t\tminAmount: 0,\r\n\t\t\t\t\ttrendAnalysis: {}\r\n\t\t\t\t},\r\n\t\t\t\tcurrentYear: new Date().getFullYear(), // 当前选择的年份\r\n\t\t\t\tnow: '',\r\n\t\t\t\tShowhiddenunits: false,\r\n\t\t\t\tshowCanvas: true,\r\n\t\t\t\t// 新增：收支概览数据\r\n\t\t\t\ttotalIncome: 0,\r\n\t\t\t\ttotalExpense: 0,\r\n\t\t\t\tnetIncome: 0,\r\n\t\t\t\t// 新增：加载状态\r\n\t\t\t\tloading: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tShowhiddenunits(newValue, oldValue) {\r\n\t\t\t\tthis.showCanvas = !newValue\r\n\t\t\t},\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.sys = uni.getSystemInfoSync();\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.bottom = 55\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.top = uni.upx2px(150);\r\n\t\t\t// #endif\r\n\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.getCurrentTime()\r\n\t\t\tthis.loadDataForCurrentTab();\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.getCurrentTime()\r\n\t\t\tthis.judgeLogin(() => {\r\n\t\t\t\tthis.loadType()\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 根据当前激活的tab加载数据\r\n\t\t\tloadDataForCurrentTab() {\r\n\t\t\t\t// 根据当前tab加载对应数据\r\n\t\t\t\tif (this.activeTabIndex === 0) {\r\n\t\t\t\t\t// 年度tab - 加载年度趋势数据\r\n\t\t\t\t\tthis.loadYearlyTrendData();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 月度tab - 加载月度、日度和概览数据\r\n\t\t\t\t\tthis.loadMonthData();\r\n\t\t\t\t\tthis.loadDayData();\r\n\t\t\t\t\tthis.updateOverviewData();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 格式化日期显示\r\n\t\t\tformatDate(dateStr) {\r\n\t\t\t\tif (!dateStr) return '';\r\n\t\t\t\tconst parts = dateStr.split('/');\r\n\t\t\t\tif (parts.length === 3) {\r\n\t\t\t\t\treturn `${parts[1]}月${parts[2]}日`;\r\n\t\t\t\t}\r\n\t\t\t\treturn dateStr;\r\n\t\t\t},\r\n\r\n\t\t\t// 更新收支概览数据\r\n\t\t\tupdateOverviewData() {\r\n\t\t\t\tthis.judgeLogin(res => {\r\n\t\t\t\t\t// 获取当月收入数据\r\n\t\t\t\t\tthis.$api.getBillStatistics({\r\n\t\t\t\t\t\tuid: res.id,\r\n\t\t\t\t\t\ter: 1, // 收入\r\n\t\t\t\t\t\ttime: this.time\r\n\t\t\t\t\t}).then(incomeRes => {\r\n\t\t\t\t\t\tthis.totalIncome = incomeRes.data.result.totalAmount || 0;\r\n\t\t\t\t\t\tthis.updateNetIncome();\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 获取当月支出数据\r\n\t\t\t\t\tthis.$api.getBillStatistics({\r\n\t\t\t\t\t\tuid: res.id,\r\n\t\t\t\t\t\ter: 0, // 支出\r\n\t\t\t\t\t\ttime: this.time\r\n\t\t\t\t\t}).then(expenseRes => {\r\n\t\t\t\t\t\tthis.totalExpense = expenseRes.data.result.totalAmount || 0;\r\n\t\t\t\t\t\tthis.updateNetIncome();\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 计算净收入\r\n\t\t\tupdateNetIncome() {\r\n\t\t\t\tthis.netIncome = this.totalIncome - this.totalExpense;\r\n\t\t\t},\r\n\r\n\t\t\t// 生成模拟年度数据（用于测试）\r\n\t\t\tgenerateMockYearlyData() {\r\n\t\t\t\t// 固定使用标准的12个月标签\r\n\t\t\t\tconst months = ['1月', '2月', '3月', '4月', '5月', '6月',\r\n\t\t\t\t\t\t\t\t'7月', '8月', '9月', '10月', '11月', '12月'];\r\n\t\t\t\tconst values = [];\r\n\r\n\t\t\t\t// 生成随机数据用于演示\r\n\t\t\t\tfor (let i = 0; i < 12; i++) {\r\n\t\t\t\t\tconst baseAmount = this.currentExpenseType === 0 ? 3000 : 5000; // 支出基数3000，收入基数5000\r\n\t\t\t\t\tconst randomVariation = Math.random() * 2000 - 1000; // ±1000的随机变化\r\n\t\t\t\t\tvalues.push(Math.max(0, baseAmount + randomVariation));\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.yearlyTrendData = {\r\n\t\t\t\t\ttitle: {\r\n\t\t\t\t\t\ttext: '',\r\n\t\t\t\t\t\tleft: 'center'\r\n\t\t\t\t\t},\r\n\t\t\t\t\ttooltip: {\r\n\t\t\t\t\t\ttrigger: 'axis',\r\n\t\t\t\t\t\tformatter: function(params) {\r\n\t\t\t\t\t\t\tif (params && params.length > 0) {\r\n\t\t\t\t\t\t\t\tconst param = params[0];\r\n\t\t\t\t\t\t\t\tconst value = param.value || 0;\r\n\t\t\t\t\t\t\t\treturn `${param.name}<br/>${param.seriesName}: ¥${value.toFixed(2)}`;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tlegend: {\r\n\t\t\t\t\t\tdata: [this.expenseTypeList[this.currentExpenseType] + '金额'],\r\n\t\t\t\t\t\tbottom: '0%'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tgrid: {\r\n\t\t\t\t\t\tleft: '3%',\r\n\t\t\t\t\t\tright: '4%',\r\n\t\t\t\t\t\tbottom: '15%',\r\n\t\t\t\t\t\ttop: '10%',\r\n\t\t\t\t\t\tcontainLabel: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\txAxis: {\r\n\t\t\t\t\t\ttype: 'category',\r\n\t\t\t\t\t\tboundaryGap: false,\r\n\t\t\t\t\t\tdata: months,\r\n\t\t\t\t\t\taxisLabel: {\r\n\t\t\t\t\t\t\tfontSize: 12\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tyAxis: {\r\n\t\t\t\t\t\ttype: 'value',\r\n\t\t\t\t\t\tname: '金额(元)',\r\n\t\t\t\t\t\taxisLabel: {\r\n\t\t\t\t\t\t\tformatter: function(value) {\r\n\t\t\t\t\t\t\t\tif (value >= 10000) {\r\n\t\t\t\t\t\t\t\t\treturn '¥' + (value / 10000).toFixed(1) + 'w';\r\n\t\t\t\t\t\t\t\t} else if (value >= 1000) {\r\n\t\t\t\t\t\t\t\t\treturn '¥' + (value / 1000).toFixed(1) + 'k';\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\treturn '¥' + value;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\tname: this.expenseTypeList[this.currentExpenseType] + '金额',\r\n\t\t\t\t\t\ttype: 'line',\r\n\t\t\t\t\t\tdata: values,\r\n\t\t\t\t\t\tsmooth: true,\r\n\t\t\t\t\t\tsymbol: 'circle',\r\n\t\t\t\t\t\tsymbolSize: 6,\r\n\t\t\t\t\t\tlineStyle: {\r\n\t\t\t\t\t\t\twidth: 3,\r\n\t\t\t\t\t\t\tcolor: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tareaStyle: {\r\n\t\t\t\t\t\t\topacity: 0.3,\r\n\t\t\t\t\t\t\tcolor: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\titemStyle: {\r\n\t\t\t\t\t\t\tcolor: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66',\r\n\t\t\t\t\t\t\tborderWidth: 2,\r\n\t\t\t\t\t\t\tborderColor: '#fff'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}]\r\n\t\t\t\t};\r\n\t\t\t},\r\n\r\n\t\t\t// 加载年度趋势数据\r\n\t\t\tloadYearlyTrendData() {\r\n\t\t\t\tthis.judgeLogin(res => {\r\n\t\t\t\t\tthis.$api.getBillYearlyTrend({\r\n\t\t\t\t\t\tuid: res.id,\r\n\t\t\t\t\t\tyear: this.currentYear,\r\n\t\t\t\t\t\ter: this.er,\r\n\t\t\t\t\t\t// categoryId: '', // 可选：特定分类\r\n\t\t\t\t\t\t// ledgerId: ''    // 可选：特定账本\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tconsole.log('年度趋势数据响应:', res.data);\r\n\r\n\t\t\t\t\t\t// 获取返回的数据\r\n\t\t\t\t\t\tconst result = res.data.result || {};\r\n\r\n\t\t\t\t\t\t// 固定使用标准的12个月标签\r\n\t\t\t\t\t\tconst months = ['1月', '2月', '3月', '4月', '5月', '6月',\r\n\t\t\t\t\t\t\t\t\t\t'7月', '8月', '9月', '10月', '11月', '12月'];\r\n\t\t\t\t\t\tconst amounts = result.amounts || [];\r\n\r\n\t\t\t\t\t\t// 确保amounts数组有12个元素，缺失的补0\r\n\t\t\t\t\t\tconst values = [];\r\n\t\t\t\t\t\tfor (let i = 0; i < 12; i++) {\r\n\t\t\t\t\t\t\tvalues.push(amounts[i] || 0);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tconsole.log('处理后的月度数据:', values);\r\n\r\n\t\t\t\t\t\t// 保存年度统计数据\r\n\t\t\t\t\t\tthis.yearlyStats = {\r\n\t\t\t\t\t\t\ttotalAmount: result.totalAmount || 0,\r\n\t\t\t\t\t\t\ttotalCount: result.totalCount || 0,\r\n\t\t\t\t\t\t\tavgAmount: result.avgAmount || 0,\r\n\t\t\t\t\t\t\tmaxAmount: result.maxAmount || 0,\r\n\t\t\t\t\t\t\tminAmount: result.minAmount || 0,\r\n\t\t\t\t\t\t\ttrendAnalysis: result.trendAnalysis || {}\r\n\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\tconsole.log('年度统计信息:', this.yearlyStats);\r\n\r\n\t\t\t\t\t\tthis.yearlyTrendData = {\r\n\t\t\t\t\t\t\ttitle: {\r\n\t\t\t\t\t\t\t\ttext: '',\r\n\t\t\t\t\t\t\t\tleft: 'center'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\ttooltip: {\r\n\t\t\t\t\t\t\t\ttrigger: 'axis',\r\n\t\t\t\t\t\t\t\tformatter: function(params) {\r\n\t\t\t\t\t\t\t\t\tif (params && params.length > 0) {\r\n\t\t\t\t\t\t\t\t\t\tconst param = params[0];\r\n\t\t\t\t\t\t\t\t\t\tconst value = param.value || 0;\r\n\t\t\t\t\t\t\t\t\t\treturn `${param.name}<br/>${param.seriesName}: ¥${value.toFixed(2)}`;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\treturn '';\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tlegend: {\r\n\t\t\t\t\t\t\t\tdata: [this.expenseTypeList[this.currentExpenseType] + '金额'],\r\n\t\t\t\t\t\t\t\tbottom: '0%'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tgrid: {\r\n\t\t\t\t\t\t\t\tleft: '3%',\r\n\t\t\t\t\t\t\t\tright: '4%',\r\n\t\t\t\t\t\t\t\tbottom: '15%',\r\n\t\t\t\t\t\t\t\ttop: '10%',\r\n\t\t\t\t\t\t\t\tcontainLabel: true\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\txAxis: {\r\n\t\t\t\t\t\t\t\ttype: 'category',\r\n\t\t\t\t\t\t\t\tboundaryGap: false,\r\n\t\t\t\t\t\t\t\tdata: result.months,\r\n\t\t\t\t\t\t\t\taxisLabel: {\r\n\t\t\t\t\t\t\t\t\tfontSize: 12\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tyAxis: {\r\n\t\t\t\t\t\t\t\ttype: 'value',\r\n\t\t\t\t\t\t\t\tname: '金额(元)',\r\n\t\t\t\t\t\t\t\taxisLabel: {\r\n\t\t\t\t\t\t\t\t\tformatter: function(value) {\r\n\t\t\t\t\t\t\t\t\t\tif (value >= 10000) {\r\n\t\t\t\t\t\t\t\t\t\t\treturn '¥' + (value / 10000).toFixed(1) + 'w';\r\n\t\t\t\t\t\t\t\t\t\t} else if (value >= 1000) {\r\n\t\t\t\t\t\t\t\t\t\t\treturn '¥' + (value / 1000).toFixed(1) + 'k';\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\treturn '¥' + value;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\tname: this.expenseTypeList[this.currentExpenseType] + '金额',\r\n\t\t\t\t\t\t\t\ttype: 'line',\r\n\t\t\t\t\t\t\t\tdata: values,\r\n\t\t\t\t\t\t\t\tsmooth: true,\r\n\t\t\t\t\t\t\t\tsymbol: 'circle',\r\n\t\t\t\t\t\t\t\tsymbolSize: 6,\r\n\t\t\t\t\t\t\t\tlineStyle: {\r\n\t\t\t\t\t\t\t\t\twidth: 3,\r\n\t\t\t\t\t\t\t\t\tcolor: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66'\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tareaStyle: {\r\n\t\t\t\t\t\t\t\t\topacity: 0.3,\r\n\t\t\t\t\t\t\t\t\tcolor: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66'\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\titemStyle: {\r\n\t\t\t\t\t\t\t\t\tcolor: this.currentExpenseType === 0 ? '#ff6b6b' : '#51cf66',\r\n\t\t\t\t\t\t\t\t\tborderWidth: 2,\r\n\t\t\t\t\t\t\t\t\tborderColor: '#fff'\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tconsole.error('加载年度趋势数据失败:', err);\r\n\t\t\t\t\t\tconsole.log('使用模拟数据作为回退方案');\r\n\t\t\t\t\t\t// 使用模拟数据作为回退方案\r\n\t\t\t\t\t\tthis.generateMockYearlyData();\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tchangeValue(val) {\r\n\t\t\t\t// this.Showhiddenunits = val;\r\n\t\t\t},\r\n\r\n\t\t\tloadDayData() {\r\n\t\t\t\tthis.judgeLogin(res => {\r\n\t\t\t\t\tthis.$api.getBillStatisticsDay( {\r\n\t\t\t\t\t\tuid: res.id,\r\n\t\t\t\t\t\ter: this.er,\r\n\t\t\t\t\t\ttime: this.now\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tconst statistics = res.data.result.statistics || [];\r\n\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\ttitle: {\r\n\t\t\t\t\t\t\t\tsubtext: '',\r\n\t\t\t\t\t\t\t\tleft: 'center'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\ttooltip: {\r\n\t\t\t\t\t\t\t\ttrigger: 'item',\r\n\t\t\t\t\t\t\t\tformatter: '{a} <br/>{b}: {c} ({d}%)'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tlegend: {\r\n\t\t\t\t\t\t\t\torient: 'horizontal',\r\n\t\t\t\t\t\t\t\tbottom: '0%',\r\n\t\t\t\t\t\t\t\tleft: 'center'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\tname: '日统计',\r\n\t\t\t\t\t\t\t\ttype: 'pie',\r\n\t\t\t\t\t\t\t\tradius: ['20%', '70%'],\r\n\t\t\t\t\t\t\t\tcenter: ['50%', '45%'],\r\n\t\t\t\t\t\t\t\tdata: statistics,\r\n\t\t\t\t\t\t\t\temphasis: {\r\n\t\t\t\t\t\t\t\t\titemStyle: {\r\n\t\t\t\t\t\t\t\t\t\tshadowBlur: 10,\r\n\t\t\t\t\t\t\t\t\t\tshadowOffsetX: 0,\r\n\t\t\t\t\t\t\t\t\t\tshadowColor: 'rgba(0, 0, 0, 0.5)'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tlabel: {\r\n\t\t\t\t\t\t\t\t\tshow: true,\r\n\t\t\t\t\t\t\t\t\tformatter: '{b}: ¥{c}'\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tlet money = res.data.result.totalAmount || 0;\r\n\t\t\t\t\t\tthis.day = this.list[this.er] + money.toFixed(2) + '元'\r\n\t\t\t\t\t\tthis.dayData = data;\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tconsole.error('加载日统计数据失败:', err);\r\n\t\t\t\t\t\t// 设置空数据状态\r\n\t\t\t\t\t\tthis.dayData = {\r\n\t\t\t\t\t\t\tseries: []\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.day = this.list[this.er] + '0.00元';\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tloadMonthData() {\r\n\t\t\t\tthis.judgeLogin(res => {\r\n\t\t\t\t\tthis.$api.getBillStatistics({\r\n\t\t\t\t\t\tuid: res.id,\r\n\t\t\t\t\t\ter: this.er,\r\n\t\t\t\t\t\ttime: this.time\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tconst billByCategory = res.data.result.billByCategory || [];\r\n\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\ttitle: {\r\n\t\t\t\t\t\t\t\tsubtext: '',\r\n\t\t\t\t\t\t\t\tleft: 'center'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\ttooltip: {\r\n\t\t\t\t\t\t\t\ttrigger: 'item',\r\n\t\t\t\t\t\t\t\tformatter: '{a} <br/>{b}: {c} ({d}%)'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tlegend: {\r\n\t\t\t\t\t\t\t\torient: 'horizontal',\r\n\t\t\t\t\t\t\t\tbottom: '0%',\r\n\t\t\t\t\t\t\t\tleft: 'center'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\tname: '月统计',\r\n\t\t\t\t\t\t\t\ttype: 'pie',\r\n\t\t\t\t\t\t\t\tradius: ['20%', '70%'],\r\n\t\t\t\t\t\t\t\tcenter: ['50%', '45%'],\r\n\t\t\t\t\t\t\t\tdata: billByCategory,\r\n\t\t\t\t\t\t\t\temphasis: {\r\n\t\t\t\t\t\t\t\t\titemStyle: {\r\n\t\t\t\t\t\t\t\t\t\tshadowBlur: 10,\r\n\t\t\t\t\t\t\t\t\t\tshadowOffsetX: 0,\r\n\t\t\t\t\t\t\t\t\t\tshadowColor: 'rgba(0, 0, 0, 0.5)'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tlabel: {\r\n\t\t\t\t\t\t\t\t\tshow: true,\r\n\t\t\t\t\t\t\t\t\tformatter: '{b}: ¥{c}'\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tlet money = res.data.result.totalAmount || 0;\r\n\t\t\t\t\t\tthis.month = this.list[this.er] + money.toFixed(2) + '元'\r\n\t\t\t\t\t\tthis.monthData = data;\r\n\r\n\t\t\t\t\t\t// 生成柱状图数据\r\n\t\t\t\t\t\tthis.generateCategoryRankData(billByCategory);\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tconsole.error('加载月统计数据失败:', err);\r\n\t\t\t\t\t\t// 设置空数据状态\r\n\t\t\t\t\t\tthis.monthData = {\r\n\t\t\t\t\t\t\tseries: []\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.month = this.list[this.er] + '0.00元';\r\n\t\t\t\t\t\t// 设置空的柱状图数据\r\n\t\t\t\t\t\tthis.generateCategoryRankData([]);\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 生成类别排行柱状图数据\r\n\t\t\tgenerateCategoryRankData(categoryData) {\r\n\t\t\t\t// 检查数据是否为空或无效\r\n\t\t\t\tif (!categoryData || categoryData.length === 0) {\r\n\t\t\t\t\tthis.categoryRankData = {\r\n\t\t\t\t\t\tcategories: [],\r\n\t\t\t\t\t\tseries: []\r\n\t\t\t\t\t};\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 过滤掉无效数据（value为0或undefined的项）\r\n\t\t\t\tconst validData = categoryData.filter(item => item && item.value && item.value > 0);\r\n\r\n\t\t\t\tif (validData.length === 0) {\r\n\t\t\t\t\tthis.categoryRankData = {\r\n\t\t\t\t\t\tcategories: [],\r\n\t\t\t\t\t\tseries: []\r\n\t\t\t\t\t};\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 按金额排序\r\n\t\t\t\tconst sortedData = validData.sort((a, b) => b.value - a.value);\r\n\r\n\t\t\t\t// 取前10个类别\r\n\t\t\t\tconst topCategories = sortedData.slice(0, 10);\r\n\r\n\t\t\t\tconst categories = topCategories.map(item => item.name || '未知类别');\r\n\t\t\t\tconst values = topCategories.map(item => item.value);\r\n\r\n\t\t\t\tthis.categoryRankData = {\r\n\t\t\t\t\tcategories: categories,\r\n\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\tname: this.list[this.er] + '金额',\r\n\t\t\t\t\t\tdata: values,\r\n\t\t\t\t\t\tcolor: this.er === 0 ? '#ff6b6b' : '#51cf66' // 支出红色，收入绿色\r\n\t\t\t\t\t}],\r\n\t\t\t\t\tyAxis: {\r\n\t\t\t\t\t\tdata: categories\r\n\t\t\t\t\t},\r\n\t\t\t\t\txAxis: {\r\n\t\t\t\t\t\tname: '金额(元)'\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\tloadType() {\r\n\t\t\t\t// this.$ajax.get('/type/load/' + this.er).then(res => {\r\n\t\t\t\t// \tthis.types = res.data\r\n\t\t\t\t// \tthis.type = this.types[0];\r\n\t\t\t\t// })\r\n\t\t\t\tthis.$api.getTypeList(this.er).then(res => {\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(res.data.success){\r\n\t\t\t\t\t\tthis.types = res.data.result\r\n\t\t\t\t\t\tthis.type = this.types[0];\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tselectType(e) {\r\n\t\t\t\tthis.type = e.data\r\n\t\t\t},\r\n\t\t\tcloseKey() {\r\n\t\t\t\tthis.show = false;\r\n\t\t\t},\r\n\t\t\topen() {\r\n\t\t\t\tthis.show = true;\r\n\t\t\t},\r\n\t\t\tselectTime(e) {\r\n\t\t\t\tthis.time = e.year + \"/\" + e.month\r\n\t\t\t\tthis.loadMonthData();\r\n\t\t\t\tthis.loadDayData();\r\n\t\t\t\tthis.updateOverviewData(); // 更新收支概览\r\n\t\t\t},\r\n\r\n\t\t\t// 选择年份\r\n\t\t\tselectYear(e) {\r\n\t\t\t\tthis.currentYear = e.year;\r\n\t\t\t\tthis.loadYearlyTrendData(); // 加载年度趋势数据\r\n\t\t\t},\r\n\r\n\t\t\tgetCurrentTime() {\r\n\t\t\t\t//获取当前时间并打印\r\n\t\t\t\tlet yy = new Date().getFullYear();\r\n\t\t\t\tlet mm = new Date().getMonth() + 1;\r\n\t\t\t\tlet dd = new Date().getDate();\r\n\t\t\t\tthis.time = yy + '/' + mm;\r\n\t\t\t\tthis.now = yy + '/' + mm + '/' + dd;\r\n\t\t\t\tthis.currentYear = yy; // 设置当前年份\r\n\t\t\t},\r\n\r\n\t\t\t// 切换主tab（年度/月度）\r\n\t\t\tchangeTab(e) {\r\n\t\t\t\tthis.activeTabIndex = e;\r\n\t\t\t\tconsole.log('切换到tab:', this.tabList[e].title);\r\n\t\t\t\tthis.loadDataForCurrentTab();\r\n\t\t\t},\r\n\r\n\t\t\t// 切换收支类型\r\n\t\t\tchangeExpenseType(index) {\r\n\t\t\t\tthis.currentExpenseType = index;\r\n\t\t\t\tthis.er = index; // 保持与原有逻辑兼容\r\n\t\t\t\tthis.activeIndex = index; // 保持与原有逻辑兼容\r\n\r\n\t\t\t\tconsole.log('切换收支类型:', this.expenseTypeList[index]);\r\n\r\n\t\t\t\t// 根据当前tab加载对应数据\r\n\t\t\t\tif (this.activeTabIndex === 0) {\r\n\t\t\t\t\t// 年度tab\r\n\t\t\t\t\tthis.loadYearlyTrendData();\r\n\t\t\t\t} else if (this.activeTabIndex === 1) {\r\n\t\t\t\t\t// 月度tab\r\n\t\t\t\t\tthis.loadType();\r\n\t\t\t\t\tthis.loadMonthData();\r\n\t\t\t\t\tthis.loadDayData();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 保留原有的change方法以兼容\r\n\t\t\tchange(e) {\r\n\t\t\t\tthis.changeExpenseType(e);\r\n\t\t\t},\r\n\t\t\tconfirm(val) {\r\n\t\t\t\t// this.show = true;\r\n\t\t\t\t// if (val <= 0) {\r\n\t\t\t\t// \tuni.$tm.toast('金额无效');\r\n\t\t\t\t// \treturn;\r\n\t\t\t\t// }\r\n\t\t\t\t// this.judgeLogin((res) => {\r\n\t\t\t\t// \tthis.$ajax.post('/bill/save', {\r\n\t\t\t\t// \t\ttype: this.type.id,\r\n\t\t\t\t// \t\ter: this.er,\r\n\t\t\t\t// \t\tvalue: val,\r\n\t\t\t\t// \t\tuid: res.id,\r\n\t\t\t\t// \t\ttime: this.time\r\n\t\t\t\t// \t}).then(res => {\r\n\t\t\t\t// \t\tthis.word = ''\r\n\t\t\t\t// \t\tthis.type = {}\r\n\t\t\t\t// \t})\r\n\t\t\t\t// });\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage,\r\n\tbody {\r\n\t\tmin-height: 100%;\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n</style>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.main-tabs-container {\r\n\t\tposition: sticky;\r\n\t\ttop: 0;\r\n\t\tz-index: 20;\r\n\t\tbackground: #667eea;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t}\r\n\r\n\t.time-selector {\r\n\t\tposition: sticky;\r\n\t\ttop: 84rpx; // 调整位置，避免被主tab遮挡\r\n\t\tz-index: 15;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t\tmin-height: 88rpx; // 确保容器有固定高度，避免闪动\r\n\r\n\t\t.time-picker-item {\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 12rpx 24rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tbackground: rgba(102, 126, 234, 0.1);\r\n\t\t\tmargin: 0 8rpx;\r\n\t\t\tborder: 1px solid rgba(102, 126, 234, 0.2);\r\n\t\t\ttransition: all 0.3s ease; // 添加平滑过渡\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: rgba(102, 126, 234, 0.2);\r\n\t\t\t\ttransform: translateY(-2rpx);\r\n\t\t\t}\r\n\r\n\t\t\t// 确保隐藏时不占用空间但保持布局稳定\r\n\t\t\t&[style*=\"display: none\"] {\r\n\t\t\t\tdisplay: none !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.overview-cards {\r\n\t\t.overview-item {\r\n\t\t\ttext-align: center;\r\n\t\t\tflex: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t.tabs-container {\r\n\t\tposition: sticky;\r\n\t\ttop: 140rpx; // 调整位置，在主tab和时间选择器之后\r\n\t\tz-index: 10;\r\n\t}\r\n\r\n\t.expense-type-tabs {\r\n\t\t.expense-type-item {\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 16rpx 24rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tmargin: 0 4rpx;\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\r\n\t\t\t&.active {\r\n\t\t\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\t\t\tcolor: white;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\ttransform: scale(1.02);\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: rgba(102, 126, 234, 0.1);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.charts-container {\r\n\t\tpadding-bottom: 40rpx;\r\n\t}\r\n\r\n\t.chart-header {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t\tpadding-bottom: 16rpx;\r\n\r\n\t\t.yearly-stats {\r\n\t\t\ttext-align: right;\r\n\r\n\t\t\t.stat-item {\r\n\t\t\t\t.stat-label {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.stat-value {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.charts-box {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 300px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.empty-state {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\theight: 100%;\r\n\t\tcolor: #999;\r\n\r\n\t\t.empty-icon {\r\n\t\t\tfont-size: 48rpx;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\topacity: 0.6;\r\n\t\t}\r\n\r\n\t\t.empty-text {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n\r\n\t// 响应式设计\r\n\t@media (max-width: 750rpx) {\r\n\t\t.overview-cards {\r\n\t\t\t.flex {\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tgap: 16rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.overview-item {\r\n\t\t\t\tpadding: 16rpx;\r\n\t\t\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775105\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=1961125f&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=1961125f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775148\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}