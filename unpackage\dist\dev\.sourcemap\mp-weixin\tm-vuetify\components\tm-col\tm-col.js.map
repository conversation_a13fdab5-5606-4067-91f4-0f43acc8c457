{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-col/tm-col.vue?8073", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-col/tm-col.vue?5216", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-col/tm-col.vue?0583", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-col/tm-col.vue?f4c4", "uni-app:///tm-vuetify/components/tm-col/tm-col.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-col/tm-col.vue?bffd", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-col/tm-col.vue?4f27"], "names": ["props", "customClass", "type", "default", "round", "color", "width", "grid", "padding", "margin", "justify", "align", "order", "maxCol", "data", "widths", "bma", "computed", "maxCol_count", "aligns", "mounted", "pd", "methods", "click", "c_width", "t"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0pB,CAAgB,wpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiB9qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,eAgBA;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IAEA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;EACA;EACAW;IACA;MAEAC;MACAC;IAEA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;EAEA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cACA;cACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACAC;QACA;MACA;MACA;QACAA;QACA;MACA;MACA;QACAA;QACA;MACA;MAEAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAAywC,CAAgB,+rCAAG,EAAC,C;;;;;;;;;;;ACA7xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-col/tm-col.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-col.vue?vue&type=template&id=e4015066&scoped=true&\"\nvar renderjs\nimport script from \"./tm-col.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-col.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-col.vue?vue&type=style&index=0&id=e4015066&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e4015066\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-col/tm-col.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-col.vue?vue&type=template&id=e4015066&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-col.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-col.vue?vue&type=script&lang=js&\"", "<!-- 需要配合，tm-row使用。，也可以单独使用。tm-col -->\r\n<template >\r\n\t<view class=\"tm-col\" :class=\"[widths?'':'tm-col-'+grid, 'ma-' + margin,'mb-'+bma[1],'mx-'+bma[0]]\" \r\n\t:style=\"{\r\n\t\twidth:widths, \r\n\t\torder: order,\r\n\t\tverticalAlign: align,\r\n\t\t\r\n\t}\">\r\n\t\r\n\t\t<view class=\"tm-col-body  \" @click=\"click\" :style=\"{\r\n\t\t textAlign:justify\r\n\t\t}\" :class=\"['pa-' + padding, aligns,` ${customClass} `,'round-'+round,color]\"><slot></slot></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * 栅格排版COL\r\n\t * @description 请注意，可以单独使用，也可搭配row使用。\r\n\t * @property {String} color = [white|blue] 主题颜色名称更多请见文档\r\n\t * @property {String} align = [top|bottom|middle] 默认top,内容纵向对齐方式\r\n\t * @property {String} justify = [left|right|center] 内容横向对齐方式\r\n\t * @property {String|Number} width = [100%] 宽度，可以是数字其它百分比，数字时单位为upx \r\n\t * @property {String|Number} grid = [1|2|3|6|12] 列宽默认1 1-12自动计算百分比。\r\n\t * @property {String|Number} padding = [0] 内间距默认0 \r\n\t * @property {String|Number} margin = [0] 外间距默认0\r\n\t * @property {String} custom-class = [] 自定义类。\r\n\t * @property {Number} round = [] 默认：0，圆角。\r\n\t * @property {Function} click = [] 点击事件\r\n\t * @property {Number} maxCol = [] 默认：12,布局的列表，比如我想一行5个，就可以用到此属性，设置为10，然后grid=2即可。\r\n\t * @property {String|Number} order = [0|1|2|3|4] 排列的顺序 默认0 可以是1-12的数字或者字符串\r\n\t */\r\nexport default {\r\n\tprops: {\r\n\t\t// 自定义类。\r\n\t\tcustomClass: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 圆角。\r\n\t\tround: {\r\n\t\t\ttype: String|Number,\r\n\t\t\tdefault: '0'\r\n\t\t},\r\n\t\t// 主题色。\r\n\t\tcolor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 自定义宽度。可以是数字，单位如：100vw,5%,auto,优先级高于grid\r\n\t\twidth: {\r\n\t\t\ttype: String | Number,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\r\n\t\t// 列宽1-12自动计算百分比。\r\n\t\tgrid: {\r\n\t\t\ttype: String | Number,\r\n\t\t\tdefault: 1\r\n\t\t},\r\n\t\t// 内间距。\r\n\t\tpadding: {\r\n\t\t\ttype: String | Number,\r\n\t\t\tdefault: '0'\r\n\t\t},\r\n\t\t// 外间距。\r\n\t\tmargin: {\r\n\t\t\ttype: String | Number,\r\n\t\t\tdefault: '0'\r\n\t\t},\r\n\t\t// 子项目横向对齐方式。 left|right|center\r\n\t\tjustify:{\r\n\t\t\ttype:String,\r\n\t\t\tdefault:'center'\r\n\t\t},\r\n\t\t// 子项目纵向对齐方式。 top|bottom|middle\r\n\t\talign:{\r\n\t\t\ttype:String,\r\n\t\t\tdefault:'top'\r\n\t\t},\r\n\t\t// 排列的顺序。\r\n\t\torder: {\r\n\t\t\ttype: String | Number,\r\n\t\t\tdefault: '0'\r\n\t\t},\r\n\t\tmaxCol:{\r\n\t\t\ttype:Number,\r\n\t\t\tdefault:12\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\r\n\t\t\twidths:'',\r\n\t\t\tbma:[0,0],\r\n\t\t\r\n\t\t};\r\n\t},\r\n\r\n\tcomputed: {\r\n\t\tmaxCol_count:function() {\r\n\t\t\treturn this.maxCol||12;\r\n\t\t},\r\n\t\taligns: function() {\r\n\t\t\tif(this.justify == 'left') return 'flex-start';\r\n\t\t\tif(this.justify == 'right') return 'flex-end';\r\n\t\t\tif(this.justify == 'center') return 'flex-center';\r\n\t\t},\r\n\r\n\t},\r\n\tasync mounted() {\r\n\t\tlet pd = this.$tm.getParentAttr(\"tm-row\",'gutter',this.$parent);\r\n\t\tif(pd) this.bma = pd;\r\n\t\tthis.$nextTick(function(){\r\n\t\t\tthis.c_width();\r\n\t\t})\r\n\r\n\t},\r\n\tmethods: {\r\n\t\tclick(e){\r\n\t\t\tthis.$emit('click',e);\r\n\t\t},\r\n\t\tc_width() {\r\n\t\t\tlet t = this;\r\n\t\t\t\t// 如果有自定义宽度，优先使用自定的宽度，否则使用grid的比例。\r\n\t\t\t\tif (t.width.indexOf('%') > -1 || t.width.indexOf('vw') > -1 || t.width.indexOf('vh') > -1) {\r\n\t\t\t\t\tt.widths = t.width;\r\n\t\t\t\t\treturn ;\r\n\t\t\t\t}\r\n\t\t\t\tif (t.width === 'auto') {\r\n\t\t\t\t\tt.widths = \"100%\";\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!isNaN(parseFloat(t.width))) {\r\n\t\t\t\t\tt.widths = uni.upx2px(parseInt(t.width)) + 'px';\r\n\t\t\t\t\treturn ;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tt.widths = (parseInt(t.grid) / t.maxCol_count) * 100 + '%';\r\n\t\t\t\t// console.log(t.maxCol_count);\r\n\t\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.tm-col{\r\n\t\theight: inherit;\r\n\t\tdisplay: inline-block;\r\n\t\t\r\n\t\t// #ifndef H5\r\n\t\theight: 100%;\r\n\t\t// #endif\r\n\t\t\r\n\t\t\r\n\t\t.tm-col-body{\r\n\t\t\tdisplay: block;\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-col.vue?vue&type=style&index=0&id=e4015066&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-col.vue?vue&type=style&index=0&id=e4015066&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775109\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}