{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-switch/tm-switch.vue?5d38", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-switch/tm-switch.vue?0da2", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-switch/tm-switch.vue?b6fc", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-switch/tm-switch.vue?cb53", "uni-app:///tm-vuetify/components/tm-switch/tm-switch.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-switch/tm-switch.vue?6d19", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-switch/tm-switch.vue?1892"], "names": ["name", "model", "prop", "event", "props", "value", "type", "default", "disabled", "loadding", "color", "black", "text", "width", "height", "offBgcolor", "fllowTheme", "watch", "computed", "black_tmeme", "color_tmeme", "changValue", "get", "set", "loaddingState", "bgColorCusjs", "data", "animationOn", "animationOff", "aniData", "mounted", "methods", "jiancMax", "onclick", "change", "checked"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+BjrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,gBAgBA;EACAA;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAP;MACAM;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;QACA;MACA;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;EACA;EACAU;IACAZ;MAEA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAa;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACAC;QACA;MACA;MACAC;QACA;QACA;QACA;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MAEA;QACA;QACA;MAEA;QACA;QACA;MACA;IAGA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC,6BAEA;EACAC;IAGA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IACAC;MAEA;QACA;UACAC;UACA9B;QACA;MACA;IAEA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACvMA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-switch/tm-switch.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-switch.vue?vue&type=template&id=454d679f&scoped=true&\"\nvar renderjs\nimport script from \"./tm-switch.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-switch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-switch.vue?vue&type=style&index=0&id=454d679f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"454d679f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-switch/tm-switch.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-switch.vue?vue&type=template&id=454d679f&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-switch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-switch.vue?vue&type=script&lang=js&\"", "<!-- 开关。 -->\r\n<template>\r\n\t<view @click=\"onclick\" class=\"d-inline-block tm-switch \" >\r\n\t\t<view class=\"tm-switch-wk relative \" :style=\"[{width:width+'rpx',height:(height+4)+'rpx'}]\">\r\n\t\t\t<view class=\"tm-switch-bg  round-l-24 round-r-24 flex-between\"\r\n\t\t\t:class=\"[bgColorCusjs,loaddingState?'opacity-7':'']\"\r\n\t\t\t\r\n\t\t\t>\r\n\t\t\t\t<view  class=\"text-size-xs tm-switch-txt text-align-center flex-center\" style=\"width:50%\">\r\n\t\t\t\t\t<text>{{text[0]?text[0]:''}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view   class=\"text-size-xs tm-switch-txt text-align-center  flex-center\" style=\"width:50%\">\r\n\t\t\t\t\t<text class=\"pr-8\">{{text[1]?text[1]:''}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<!-- bar -->\r\n\t\t\t<view  class=\"tm-switch-wk-bar absolute round-24 white  flex-center \" \r\n\t\t\t:style=\"[\r\n\t\t\t\t{transform:changValue?`translateX(${(width-(width/2+4))}rpx)`:`translateX(2rpx)`},\r\n\t\t\t\t{width:(width/2-4)+'rpx',height:(height-4)+'rpx'}\r\n\t\t\t]\"\r\n\t\t\t:class=\"[changValue?'shadow-'+color+'-10':'',changValue?'on aniOn':'aniOff']\">\r\n\t\t\t\t<text v-if=\"loaddingState\" class=\"iconfont icon-loading\" :class=\"[loaddingState?'load':'',`text-${color_tmeme}`]\"></text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * 开关\r\n\t * @property {String|Boolean} value = [true|false] 同v-model一样的值，如果需要双向绑定需要value.sync，推荐使用v-model\r\n\t * @property {String|Number|Boolean|Object} name = [] 自定义数据，change时携带。\r\n\t * @property {Function} change {checked,value:携带的name数据}\r\n\t * @property {Boolean} disabled = [true|false] 默认：false, 禁用\r\n\t * @property {String} color = [] 默认：primary, 主题色\r\n\t * @property {Boolean} black = [true|false] 默认：false, 暗黑模式\r\n\t * @property {Boolean} loadding = [true|false] 默认：false, 是否加载中\r\n\t * @property {Array} text = [true|false] 默认： ['开','关'], 左右两边的字符\r\n\t * @property {Number} width = [] 默认：100,单位rpx，宽度\r\n\t * @property {Number} height = [] 默认：50,单位rpx，高度\r\n\t * @property {String} offBgcolor = [] 默认：'grey-lighten-2 text-grey', 主题色可以是文字色组合来改变文字和背景色.\r\n\t * @example <tm-switch v-model=\"checked\"></tm-switch>\r\n\t * \r\n\t */\r\n\texport default {\r\n\t\tname: 'tm-switch',\r\n\t\tmodel: {\r\n\t\t\tprop: 'value',\r\n\t\t\tevent: 'input'\r\n\t\t},\r\n\t\tprops:{\r\n\t\t\tvalue:{\r\n\t\t\t\ttype:String|Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t},\r\n\t\t\tname:{\r\n\t\t\t\ttype:String|Number|Boolean|Object,\r\n\t\t\t\tdefault:''\r\n\t\t\t},\r\n\t\t\t// 禁用。\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:false\r\n\t\t\t},\r\n\t\t\tloadding: {\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:false\r\n\t\t\t},\r\n\t\t\tcolor:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'primary'\r\n\t\t\t},\r\n\t\t\t// 暗黑\r\n\t\t\tblack: {\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:null\r\n\t\t\t},\r\n\t\t\t// 左右两边的字符。\r\n\t\t\ttext:{\r\n\t\t\t\ttype:Array,\r\n\t\t\t\tdefault:()=>{\r\n\t\t\t\t\treturn ['开','关']\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\twidth:{\r\n\t\t\t\ttype:String|Number,\r\n\t\t\t\tdefault:100\r\n\t\t\t},\r\n\t\t\theight:{\r\n\t\t\t\ttype:String|Number,\r\n\t\t\t\tdefault:50\r\n\t\t\t},\r\n\t\t\toffBgcolor:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'grey-lighten-2 text-grey'\r\n\t\t\t},\r\n\t\t\t// 跟随主题色的改变而改变。\r\n\t\t\tfllowTheme:{\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:true\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tvalue: function(newval, oldval) {\r\n\t\t\t\t\r\n\t\t\t\tif (newval !== oldval) {\r\n\t\t\t\t\tif (!this.jiancMax()) {\r\n\t\t\t\t\t\tthis.changValue = false;\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tblack_tmeme: function() {\r\n\t\t\t\tif (this.black !== null) return this.black;\r\n\t\t\t\treturn this.$tm.vx.state().tmVuetify.black;\r\n\t\t\t},\r\n\t\t\tcolor_tmeme:function(){\r\n\t\t\t\tif(this.$tm.vx.state().tmVuetify.color!==null&&this.$tm.vx.state().tmVuetify.color && this.fllowTheme){\r\n\t\t\t\t\treturn this.$tm.vx.state().tmVuetify.color;\r\n\t\t\t\t}\r\n\t\t\t\treturn this.color;\r\n\t\t\t},\r\n\t\t\tchangValue: {\r\n\t\t\t\tget: function() {\r\n\t\t\t\t\treturn this.value;\r\n\t\t\t\t},\r\n\t\t\t\tset: function(newValue) {\r\n\t\t\t\t\tthis.$emit('input', newValue)\r\n\t\t\t\t\t// 如果不想用v-model. 直接使用value,需要:value.sync\r\n\t\t\t\t\tthis.$emit('update:value', newValue);\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tloaddingState:function(){\r\n\t\t\t\treturn this.loadding;\r\n\t\t\t},\r\n\t\t\tbgColorCusjs(){\r\n\t\t\t\tif(this.disabled) {\r\n\t\t\t\t\tif(this.black_tmeme){\r\n\t\t\t\t\t\treturn 'grey-darken-4   bk';\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\treturn this.color_tmeme+' opacity-5';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif(this.black_tmeme){\r\n\t\t\t\t\tif(this.changValue) return this.color_tmeme + ' bk ';\r\n\t\t\t\t\tif(!this.changValue) return 'grey-darken-1 bk ';\r\n\t\t\t\t\t\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(this.changValue) return this.color_tmeme + ' text-white ';\r\n\t\t\t\t\tif(!this.changValue) return this.offBgcolor;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tanimationOn:null,\r\n\t\t\t\tanimationOff:null,\r\n\t\t\t\taniData:null,\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t// 检查是否超过了最大选择。\r\n\t\t\tjiancMax() {\r\n\t\t\t\tif(this.disabled) return false;\r\n\t\t\t\treturn true;\r\n\t\t\t},\r\n\t\t\tonclick(e) {\r\n\t\t\t\tif (this.disabled||this.loaddingState) return;\r\n\t\t\t\tif (!this.jiancMax()) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.changValue = !this.changValue;\r\n\t\t\t\tthis.change();\r\n\t\t\t},\r\n\t\t\tchange() {\r\n\t\t\t\t\r\n\t\t\t\tthis.$nextTick(function(){\r\n\t\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\t\tchecked: this.changValue,\r\n\t\t\t\t\t\tvalue: this.name\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tm-switch{\r\n\tvertical-align: middle;\r\n\t.tm-switch-wk{\r\n\t\t// width: 100rpx;\r\n\t\t// height: 52rpx;\r\n\t}\r\n\t.tm-switch-bg{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\ttransition: all 0.5s;\r\n\t\t\r\n\t\t.tm-switch-txt{\r\n\t\t\t// width: 50upx;\r\n\t\t\theight: 100%;\r\n\t\t\t// line-height: 48upx;\r\n\t\t}\r\n\t}\r\n\t.tm-switch-wk-bar{\r\n\t\tleft: 4rpx;\r\n\t\ttop: 4rpx;\r\n\t\t// width:40rpx;\r\n\t\t// height: 46rpx;\r\n\t\ttransition: all 0.35s ease-in-out;\r\n\t\t.load{\r\n\t\t\tanimation: xhRote 0.8s infinite linear;\r\n\t\t}\r\n\t}\r\n}\r\n.aniOn{\r\n\tleft: inherit;\r\n\t\r\n}\r\n.aniOff{\r\n\tright: inherit;\r\n}\r\n\r\n\r\n\r\n\r\n@keyframes xhRote{\r\n\t0%{\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\t\r\n\t100%{\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-switch.vue?vue&type=style&index=0&id=454d679f&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-switch.vue?vue&type=style&index=0&id=454d679f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775139\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}