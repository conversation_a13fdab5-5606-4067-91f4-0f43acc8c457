{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-input/tm-input.vue?603c", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-input/tm-input.vue?5581", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-input/tm-input.vue?d8e7", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-input/tm-input.vue?3bba", "uni-app:///tm-vuetify/components/tm-input/tm-input.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-input/tm-input.vue?374c", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-input/tm-input.vue?5b68"], "names": ["components", "tmSheet", "tmIcons", "name", "props", "type", "default", "prefixpText", "flat", "showIndent", "maxlength", "black", "focusShow", "titleFontSize", "height", "verify", "check", "text", "titleClass", "required", "adjustPosition", "autoFocus", "confirmType", "disabled", "password", "inputType", "value", "rightIcon", "prefixpIcon", "prefixpIconColor", "leftIcon", "suffix", "suffixIcon", "suffixIconColor", "title", "fontSize", "align", "clear", "color", "bgColor", "borderColor", "borderBottom", "textColor", "placeholder", "placeholderClass", "vertical", "round", "bgRound", "bgShadow", "bgTheme", "focus", "padding", "fllowTheme", "data", "showError", "errorText", "FOCUS_Auto", "isFocus", "computed", "_required", "height_rpx", "font_size", "title_size", "focus_fs", "get", "set", "black_tmeme", "color_tmeme", "valueLen", "valdata", "mounten", "methods", "onclickInput", "input", "verifyInput", "clearVerify", "blur", "focusFun", "clearVal"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA4pB,CAAgB,0pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC0KhrB;EACAA;IAAAC;IAAAC;EAAA;EACAC;EACAC;IACA;IACAD;MACAE;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;QACA;UACA;YACAU;YACAC;UACA;QACA;MACA;IACA;IACAC;MACAb;MACAC;IACA;IACA;IACAa;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;MACApB;MACAC;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;IACAuB;MACAxB;MACAC;IACA;IACA;IACAwB;MACAzB;MACAC;IACA;IACA;IACAyB;MACA1B;MACAC;IACA;IACA;IACA0B;MACA3B;MACAC;IACA;IACA2B;MACA5B;MACAC;IACA;IACA;IACA4B;MACA7B;MACAC;IACA;IACA6B;MACA9B;MACAC;IACA;IACA;IACA8B;MACA/B;MACAC;IACA;IACA;IACA+B;IACA;IACAC;MACAjC;MACAC;IACA;IACA;IACAiC;MACAlC;MACAC;IACA;IACA;IACAkC;MACAnC;MACAC;IACA;IACA;IACAmC;MACApC;MACAC;IACA;IACA;IACAoC;MACArC;MACAC;IACA;IACAqC;MACAtC;MACAC;IACA;IACAsC;MACAvC;MACAC;IACA;IACA;IACAuC;IACAC;MACAzC;MACAC;IACA;IACAyC;MACA1C;MACAC;IACA;IACA0C;MACA3C;MACAC;IACA;IACA2C;MACA5C;MACAC;IACA;IACA;IACA4C;MACA7C;MACAC;IACA;IACA6C;MACA9C;MACAC;QACA;MACA;IACA;IACA;IACA8C;MACA/C;MACAC;IACA;EACA;EACA+C;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;IACA;IACAC;MACAC;QACA;MACA;MACAC;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACAL;QACA;MACA;MACAC;QACA;QACA;QACA;UACA;YACA;UACA;QACA;MACA;IACA;EACA;EACAK;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAEA;MACA3D;MACA;QACAA;MACA;MAEA;MACA;MACA;MACA;IACA;IACA;IACA4D;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AAEA;AAAA,4B;;;;;;;;;;;;ACteA;AAAA;AAAA;AAAA;AAA2wC,CAAgB,isCAAG,EAAC,C;;;;;;;;;;;ACA/xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-input/tm-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-input.vue?vue&type=template&id=3b4f5966&scoped=true&\"\nvar renderjs\nimport script from \"./tm-input.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-input.vue?vue&type=style&index=0&id=3b4f5966&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3b4f5966\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-input/tm-input.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-input.vue?vue&type=template&id=3b4f5966&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  _vm.$initSSP()\n  var g0 = _vm.clear && _vm.valdata.length != \"\"\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n  if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n    _vm.$setSSP(\"default\", {\n      title: _vm.title,\n    })\n  }\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-input.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"d-block tm-input overflow\"\r\n\t:class=\"[\r\n\t\t\r\n\t\tblack_tmeme?(bgTheme?'grey-darken-5':''):bgTheme,\r\n\t\tflat?'':`px-${padding[0]}`,\r\n\t\t'round-'+ bgRound,\r\n\t\t`shadow-${color}-${bgShadow}`\r\n\t]\"\r\n\t>\r\n\t\t<view \r\n\t\t\t:class=\"(flat?'':`  py-${padding[1]} `)+` ${borderBottom?black_tmeme?'border-grey-darken-4-b-1 ':'border-grey-lighten-4-b-1':''}`\"\r\n\t\t\t>\r\n\t\t\t<view @click=\"onclickInput\" :class=\"[vertical?'tm-input-col':'flex-between ']\" :style=\"{\r\n\t\t\t\twidth: '100%',height: 'auto',\r\n\t\t\t\talignItems:inputType=='textarea'?'flex-start':'center'\r\n\t\t\t}\">\r\n\t\t\t\t<!-- 左边内容。 -->\r\n\t\t\t\t<view v-if=\"leftIcon||title\" class=\"tm-input-left flex-start flex-shrink\" :class=\"[vertical?'pb-24':'']\">\r\n\t\t\t\t\t<!-- icon -->\r\n\t\t\t\t\t<view v-if=\"leftIcon\" class=\"pr-16 vertical-align-middle flex-center\">\r\n\t\t\t\t\t\t<tm-icons dense  :name=\"leftIcon\" :color=\"color_tmeme\"></tm-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 标题 -->\r\n\t\t\t\t\t<view v-if=\"title\" class=\"d-inline-block  \"\r\n\t\t\t\t\t:style=\"{fontSize:title_size}\"\r\n\t\t\t\t\t\t:class=\"[titleClass,black_tmeme?'bk  text-grey-lighten-3':'']\">\r\n\t\t\t\t\t\t<text v-if=\"_required\" class=\"text-red\">*</text>\r\n\t\t\t\t\t\t<slot name=\"default\" :title=\"title\">\r\n\t\t\t\t\t\t\t{{title}}\r\n\t\t\t\t\t\t</slot>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view  class=\"flex-between \" :class=\"[disabled?'opacity-6':'',]\" style=\"width: 100%;\">\r\n\r\n\t\t\t\t\t<!-- input主体 -->\r\n\t\t\t\t\t<view class=\"tm-input-center relative fulled\" >\r\n\t\t\t\t\t\t<view class=\"flex-start   tm-input-center-wk\"\r\n\t\t\t\t\t\t\t:class=\"['round-'+round,showIndent?'px-16':'',\r\n\t\t\t\t\t\t\tblack_tmeme?(bgColor?'grey-darken-4  text-grey-lighten-3':'text-grey-lighten-3'):bgColor,\r\n\t\t\t\t\t\t\t`text-${textColor}`,isFocus&&focusShow?(black_tmeme?`border-${color_tmeme}-a-1`:`${color_tmeme} outlined `):``,\r\n\t\t\t\t\t\t\t`border-${black_tmeme?(borderColor?'grey-darken-4':''):borderColor}-a-1`,\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t]\">\r\n\t\t\t\t\t\t\t<view class=\"flex-shrink px-16 flex-center\" v-if=\"prefixpIcon\" style=\"line-height: 0;\">\r\n\t\t\t\t\t\t\t\t<tm-icons dense  :name=\"prefixpIcon\" :size=\"28\" :color=\"(prefixpIconColor||color_tmeme)\" ></tm-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view :style=\"{fontSize:font_size}\" class=\"flex-shrink pr-24\" :class=\"[titleClass,black_tmeme?'bk  text-grey-lighten-3':'']\" v-if=\"prefixpText\">\r\n\t\t\t\t\t\t\t\t{{prefixpText}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<input \talways-embed v-if=\"inputType!='textarea'\"  @confirm=\"$emit('confirm',$event)\" @input=\"input\"\r\n\t\t\t\t\t\t\t\t@keyboardheightchange=\"$emit('keyboardheightchange',$event)\" @blur=\"blur\"\r\n\t\t\t\t\t\t\t\t@focus=\"focusFun\" :focus=\"focus_fs\" :maxlength=\"maxlength\" :adjust-position=\"adjustPosition\"\r\n\t\t\t\t\t\t\t\t:auto-focus=\"autoFocus\" :confirm-type=\"confirmType\" :disabled=\"disabled\" \r\n\t\t\t\t\t\t\t\t:password=\"password\"  :type=\"inputType\" :value=\"value\" class=\"tm-input-center-input \"\r\n\t\t\t\t\t\t\t\t:class=\"['text-align-'+align,showError?'text-red':'',' py-5 ']\" :placeholder=\"placeholder\"\r\n\t\t\t\t\t\t\t\t:placeholder-class=\"black_tmeme? 'text-grey-darken-1 ':'  ' +` text-size-n ` + placeholderClass\"\r\n\t\t\t\t\t\t\t\t :style=\"{\r\n\t\t\t\t\t\t\t\t\tfontSize:font_size,\r\n\t\t\t\t\t\t\t\t \theight:height_rpx+'rpx'\r\n\t\t\t\t\t\t\t\t }\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<!-- uniapp的bug，当输入框禁用时，点击此处来获取新的焦点，以让键盘收起。 -->\r\n\t\t\t\t\t\t\t<view v-if=\"disabled\" class=\"absolute fulled t-0 r-0\" :style=\"{\r\n\t\t\t\t\t\t\t\t \theight:height_rpx+'rpx'\r\n\t\t\t\t\t\t\t\t }\"></view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<textarea always-embed v-if=\"inputType=='textarea'\"  @confirm=\"$emit('confirm',$event)\" @input=\"input\"\r\n\t\t\t\t\t\t\t\t@keyboardheightchange=\"$emit('keyboardheightchange',$event)\" @blur=\"blur\"\r\n\t\t\t\t\t\t\t\t@focus=\"focusFun\" :focus=\"focus_fs\" :maxlength=\"maxlength\" :adjust-position=\"adjustPosition\"\r\n\t\t\t\t\t\t\t\t:auto-focus=\"autoFocus\"  :confirm-type=\"confirmType\" :disabled=\"disabled\"\r\n\t\t\t\t\t\t\t\t:value=\"value\"  class=\"tm-input-center-input \" :style=\"{\r\n\t\t\t\t\t\t\t\t\theight:height_rpx+'rpx',\r\n\t\t\t\t\t\t\t\t\tfontSize:font_size,\r\n\t\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t\t:class=\"[maxlength>0?'pb-46':'','text-align-'+align,showError?'text-red':'','pt-16 fulled']\" :placeholder=\"placeholder\"\r\n\t\t\t\t\t\t\t\t:placeholder-class=\"black_tmeme? 'text-grey-darken-1 ':' text-grey-lighten-1 ' +` text-size-n `+ placeholderClass\" >\t\r\n\t\t\t\t\t\t\t</textarea>\r\n\t\t\t\t\t\t\t<!-- 清除图标 -->\r\n\t\t\t\t\t\t\t<view v-if=\"clear&&valdata.length!=''\" class=\"flex-center pl-16\">\r\n\t\t\t\t\t\t\t\t<tm-icons @click.stop=\"clearVal\" name=\"icon-times-circle-fill\" :color=\"color_tmeme\"></tm-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view v-if=\"suffixIcon\" class=\"flex-center\">\r\n\t\t\t\t\t\t\t\t<tm-icons :size=\"26\" :name=\"suffixIcon\" :color=\"(suffixIconColor||color_tmeme)\"></tm-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"maxlength>0&&inputType=='textarea'\" \r\n\t\t\t\t\t\t:style=\"{bottom:'16rpx',right:'16rpx'}\"\r\n\t\t\t\t\t\tclass=\"tm-input-center-numXz text-align-right text-size-xxs pt-12 text-grey absolute fulled\">{{valueLen}}/{{maxlength}}</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 右边。 -->\r\n\t\t\t\t\t<view class=\"tm-input-right flex-end flex-shrink\">\r\n\t\t\t\t\t\t<!-- 后缀文字，比如单位，等 -->\r\n\t\t\t\t\t\t<text v-if=\"suffix\" class=\" text-grey-darken-4 pl-10\" :style=\"{fontSize:font_size}\">{{suffix}}</text>\r\n\t\t\t\t\t\t<!-- 后台图标。 -->\r\n\t\t\t\t\t\t<view v-if=\"rightIcon\" class=\"pl-10\" style=\"line-height: 0;\">\r\n\t\t\t\t\t\t\t<tm-icons dense  :name=\"rightIcon\" color=\"grey-lighten-1\"></tm-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 插入的按钮等内容。 -->\r\n\t\t\t\t\t\t<slot name=\"rightBtn\"></slot>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t</view>\r\n\t\t\t<!-- detail出错成功等信息。 -->\r\n\t\t\t<view v-if=\"showError\" class=\"text-size-xs text-red pt-12\">{{errorText}}</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * 输入框\r\n\t * @property {Number} maxlength = [-1] 默认：-1，最大输入字符数。\r\n\t * @property {Boolean} black = [] 默认：false，暗黑模式。\r\n\t * @property {Function} verify = [] 默认： (val) => {check: val.length <= 0 ? false : true,text: \"必填项不能为空。\"}，校验规则函数。\r\n\t * @property {String} title-class = [] 默认： text-grey-darken-4,自定左边标题或者上标题的类。\r\n\t * @property {Boolean} required = [] 默认： false, 是否是必填。如果必填写将会触发基础的校验，不能为空。\r\n\t * @property {Boolean} adjust-position = [] 默认： false, 是否上推键盘。\r\n\t * @property {Boolean} auto-focus = [] 默认： false, 自动获得焦点。\r\n\t * @property {String} confirm-type = [done|go|next|send|search] 默认： done, 键盘右下角确认按钮文字。\r\n\t * @property {Boolean} disabled = [] 默认： false, 禁用。\r\n\t * @property {Boolean} focus-show = [] 默认： false, 是否显示聚焦状态。\r\n\t * @property {Boolean} show-indent = [] 默认： false, 是滞使输入框内容两边缩进。默认是。\r\n\t * @property {Boolean} password = [] 默认： false, 密码模式\r\n\t * @property {String} input-type = [digit|text|number|password|idcard|textarea] 默认： text, 输入模式 \r\n\t * @property {String} value = [] 默认： \"\", 输入内容，同v-model\r\n\t * @property {String} right-icon = [] 默认： \"\", 后缀图标\r\n\t * @property {String} left-icon = [] 默认： \"\", 外层前缀图标\r\n\t * @property {String} prefixp-icon = [] 默认： \"\", 输入框内部前缀图标\r\n\t * @property {String} prefixp-icon-color = [] 默认： \"\", 默认空，使用主题color颜色\r\n\t * @property {String} suffix = [] 默认： \"\", 后缀文字\r\n\t * @property {String} suffix-icon = [] 默认： \"\", 输入框内后缀图标\r\n\t * @property {String} suffix-icon-color = [] 默认： \"\", 默认使用主题图标\r\n\t * @property {String} title = [] 默认： \"\", 左边标题。\r\n\t * @property {String} title-font-size = [xxs/xs/s/n/g/lg/xl] 默认： \"n\",同类的字号,xxs,xs,s,n,g,lg,xl\r\n\t * @property {Number|String} font-size = [xxs/xs/s/n/g/lg/xl/任意数字] 默认： \"n\",同类的字号,xxs,xs,s,n,g,lg,xl，也可以是数字单位rpx\r\n\t * @property {String} align = [left|center|right] 默认： \"\", 输入框文字对齐方式。left,center,right\r\n\t * @property {Boolean} clear = [false|true] 默认： false, 显示清除图标。\r\n\t * @property {String} color = [] 默认： primary, 主题色名称\r\n\t * @property {String} bg-color = [grey-lighten-5|white] 默认： grey-lighten-5, 输入框背景色。\r\n\t * @property {String} border-color = [] 默认： \"\", 输入框边框类型主题颜色名称。\r\n\t * @property {Boolean} border-bottom = [false|true] 默认： true, 是否显示下划线\r\n\t * @property {String} text-color = [black|primary] 默认： black, 输入框文字颜色。\r\n\t * @property {String} placeholder = [] 默认： 请输入, 占位文字\r\n\t * @property {Boolean} vertical = [false|true] 默认： false, 是否上下排列\r\n\t * @property {Number} round = [] 默认： 2, 输入框圆角。\r\n\t * @property {Boolean} showIndent = [] 默认： true, 是否输入框内部两边缩进\r\n\t * @property {Number} bg-round = [] 默认： 0, 整体框圆角。\r\n\t * @property {Number} bg-shadow = [] 默认： 0, 整体框投影。\r\n\t * @property {String} bg-theme = [] 默认：white, 整体框背景\r\n\t * @property {Boolean} flat = [] 默认： false, 是否去除所有边框\r\n\t * @property {Number|String} height = [] 默认： 68, \r\n\t * @property {String} name = [] 默认：''，提交表单时的的字段名称标识\r\n\t * @property {String} prefixp-text = [] 默认：''，输入框内前缀文字\r\n\t * @property {String} placeholder-class = [] 默认：''，点位符的自定义类。\r\n\t * @property {Array} padding = [] 默认：[32,12]，左右，上下内间距。\r\n\t * @property {Function} click 点击输入框时触发发的函数。\r\n\t * @property {Function} clear 清空时触发携带相关数据\r\n\t * @property {Function} input 输入时触发携带相关数据\r\n\t * @example <tm-input ></tm-input>\r\n\t * \r\n\t */\r\n\timport tmSheet from \"@/tm-vuetify/components/tm-sheet/tm-sheet.vue\"\r\n\timport tmIcons from \"@/tm-vuetify/components/tm-icons/tm-icons.vue\"\r\n\texport default {\r\n\t\tcomponents:{tmSheet,tmIcons},\r\n\t\tname:\"tm-input\",\r\n\t\tprops: {\r\n\t\t\t//提交表单时的的字段名称\r\n\t\t\tname:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:''\r\n\t\t\t},\r\n\t\t\tprefixpText:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:''\r\n\t\t\t},\r\n\t\t\tflat: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t//是否输入框内部两边缩进。默认是\r\n\t\t\tshowIndent:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:true\r\n\t\t\t},\r\n\t\t\tmaxlength: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: -1\r\n\t\t\t},\r\n\t\t\tblack: {\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:null\r\n\t\t\t},\r\n\t\t\t//是否显示聚焦状态\r\n\t\t\tfocusShow: {\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:false\r\n\t\t\t},\r\n\t\t\ttitleFontSize:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'n',//同类的字号,xxs,xs,s,n,g,lg,xl\r\n\t\t\t},\r\n\t\t\theight:{\r\n\t\t\t\ttype:Number|String,\r\n\t\t\t\tdefault:68\r\n\t\t\t},\r\n\t\t\t// 校验规则函数。\r\n\t\t\tverify: {\r\n\t\t\t\ttype: Function,\r\n\t\t\t\tdefault: ()=>{\r\n\t\t\t\t\treturn (val) => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tcheck: val?.length <= 0 ? false : true,\r\n\t\t\t\t\t\t\ttext: \"必填项不能为空。\"\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttitleClass: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'text-grey-darken-4'\r\n\t\t\t},\r\n\t\t\t// 是否是必填。如果必填写将会触发基础的校验，不能为空。\r\n\t\t\trequired: Boolean,\r\n\t\t\tadjustPosition: Boolean,\r\n\t\t\tautoFocus: Boolean,\r\n\t\t\tconfirmType: String,\r\n\t\t\tdisabled: Boolean,\r\n\t\t\tpassword: Boolean,\r\n\t\t\tinputType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'text'\r\n\t\t\t},\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: String|Number,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 右边外层后缀图标。\r\n\t\t\trightIcon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 输入框内部前缀图标。\r\n\t\t\tprefixpIcon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tprefixpIconColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 左边外层图标。\r\n\t\t\tleftIcon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 后缀文字\r\n\t\t\tsuffix: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 输入框后缀图标\r\n\t\t\tsuffixIcon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tsuffixIconColor:{\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 左边标题。\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tfontSize:{\r\n\t\t\t\ttype:Number|String,\r\n\t\t\t\tdefault:'n'\r\n\t\t\t},\r\n\t\t\t// 输入框文字对齐方式。left,center,right\r\n\t\t\talign: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'left'\r\n\t\t\t},\r\n\t\t\t// 显示清除图标。\r\n\t\t\tclear: Boolean,\r\n\t\t\t// 主题色名称。\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'primary'\r\n\t\t\t},\r\n\t\t\t//输入框背景色。grey-lighten-5\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 输入框边框类型主题颜色名称。\r\n\t\t\tborderColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否显示下划线\r\n\t\t\tborderBottom: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// text输入框文字颜色。\r\n\t\t\ttextColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'black'\r\n\t\t\t},\r\n\t\t\tplaceholder: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"请输入\"\r\n\t\t\t},\r\n\t\t\tplaceholderClass:{\n\t\t\t\ttype:String,\n\t\t\t\tdefault:'text-grey-lighten-1'\n\t\t\t},\r\n\t\t\t// 是否上下排列输入框。\r\n\t\t\tvertical: Boolean,\r\n\t\t\tround:{\r\n\t\t\t\ttype:Number|String,\r\n\t\t\t\tdefault:2\r\n\t\t\t},\r\n\t\t\tbgRound:{\r\n\t\t\t\ttype:Number|String,\r\n\t\t\t\tdefault:0\r\n\t\t\t},\r\n\t\t\tbgShadow:{\r\n\t\t\t\ttype:Number|String,\r\n\t\t\t\tdefault:0\r\n\t\t\t},\r\n\t\t\tbgTheme:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'white'\r\n\t\t\t},\r\n\t\t\t//获取焦点。\r\n\t\t\tfocus:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t},\r\n\t\t\tpadding:{\r\n\t\t\t\ttype:Array,\r\n\t\t\t\tdefault:()=>{\r\n\t\t\t\t\treturn [32,12];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 跟随主题色的改变而改变。\r\n\t\t\tfllowTheme:{\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowError: false,\r\n\t\t\t\terrorText: \"请正确填写\",\r\n\t\t\t\tFOCUS_Auto:false,\r\n\t\t\t\tisFocus:false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t_required(){\r\n\t\t\t\treturn this.required\r\n\t\t\t},\r\n\t\t\theight_rpx:function(){\r\n\t\t\t\treturn this.height;\r\n\t\t\t},\r\n\t\t\tfont_size:function () {\r\n\t\t\t\t\r\n\t\t\t\tlet font = {\r\n\t\t\t\t\t'xxs':'20rpx',\r\n\t\t\t\t\t'xs':'22rpx',\r\n\t\t\t\t\t's':'24rpx',\r\n\t\t\t\t\t'm':'26rpx',\r\n\t\t\t\t\t'n':'28rpx',\r\n\t\t\t\t\t'g':'32rpx',\r\n\t\t\t\t\t'lg':'36rpx',\r\n\t\t\t\t\t'xl':'40rpx'\r\n\t\t\t\t}\r\n\t\t\t\tif(typeof this.fontSize=='string') return font[this.fontSize];\r\n\t\t\t\treturn this.fontSize+'rpx';\r\n\t\t\t},\r\n\t\t\ttitle_size:function () {\r\n\t\t\t\tlet font = {\r\n\t\t\t\t\t'xxs':'20rpx',\r\n\t\t\t\t\t'xs':'22rpx',\r\n\t\t\t\t\t's':'24rpx',\r\n\t\t\t\t\t'm':'26rpx',\r\n\t\t\t\t\t'n':'28rpx',\r\n\t\t\t\t\t'g':'32rpx',\r\n\t\t\t\t\t'lg':'36rpx',\r\n\t\t\t\t\t'xl':'40rpx'\r\n\t\t\t\t}\r\n\t\t\t\tif(typeof this.titleFontSize=='string') return font[this.titleFontSize];\r\n\t\t\t\treturn this.titleFontSize+'rpx';\r\n\t\t\t},\r\n\t\t\tfocus_fs:{\r\n\t\t\t\tget:function(){\r\n\t\t\t\t\treturn this.FOCUS_Auto;\r\n\t\t\t\t},\r\n\t\t\t\tset:function(val){\r\n\t\t\t\t\tthis.FOCUS_Auto = val;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tblack_tmeme: function() {\r\n\t\t\t\tif (this.black !== null) return this.black;\r\n\t\t\t\treturn this.$tm.vx.state().tmVuetify.black;\r\n\t\t\t},\r\n\t\t\tcolor_tmeme:function(){\r\n\t\t\t\tif(this.$tm.vx.state().tmVuetify.color!==null&&this.$tm.vx.state().tmVuetify.color && this.fllowTheme){\r\n\t\t\t\t\treturn this.$tm.vx.state().tmVuetify.color;\r\n\t\t\t\t}\r\n\t\t\t\treturn this.color;\r\n\t\t\t},\r\n\t\t\tvalueLen:function(){\r\n\t\t\t\t// 为了兼容ios不能使用this.valdata.length.\r\n\t\t\t\tlet p = String(this.valdata);\r\n\t\t\t\treturn p?.split('').length||0 ;\r\n\t\t\t},\r\n\t\t\tvaldata:{\r\n\t\t\t\tget:function(){\r\n\t\t\t\t\treturn this.value;\r\n\t\t\t\t},\r\n\t\t\t\tset:function(val){\r\n\t\t\t\t\tthis.$emit('input', val)\r\n\t\t\t\t\tthis.$emit('update:value', val)\r\n\t\t\t\t\tif (this._required) {\r\n\t\t\t\t\t\tthis.$nextTick(function(){\r\n\t\t\t\t\t\t\tthis.verifyInput();\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounten(){\r\n\t\t\tthis.FOCUS_Auto = this.focus;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonclickInput(e){\r\n\t\t\t\tthis.$emit('click',e)\r\n\t\t\t},\r\n\t\t\tinput(e) {\r\n\t\t\t\tthis.valdata = e.target.value;\r\n\t\t\t},\r\n\t\t\t// 校验是否通过。\r\n\t\t\tverifyInput() {\r\n\t\t\t\t\r\n\t\t\t\tlet verify = this.verify.bind(this, this.valdata||'');\r\n\t\t\t\tverify = verify.call(this,this.valdata||'')\r\n\t\t\t\tif(typeof verify ==='function'){\r\n\t\t\t\t\tverify = verify.call(this,this.valdata||'')\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (typeof verify !== 'object') verify = {};\r\n\t\t\t\tthis.showError = !(verify.check??true);\r\n\t\t\t\tthis.errorText = verify.text??\"\";\r\n\t\t\t\treturn verify.check??true;\r\n\t\t\t},\r\n\t\t\t//清除校验显示 的内容。\r\n\t\t\tclearVerify() {\r\n\t\t\t\tthis.showError = false;\r\n\t\t\t\tthis.errorText = \"\";\r\n\t\t\t},\r\n\t\t\tblur(e) {\r\n\t\t\t\tthis.isFocus=false;\r\n\t\t\t\tthis.$emit('blur', e)\r\n\t\t\t},\r\n\t\t\tfocusFun(e){\r\n\t\t\t\tthis.isFocus=true;\r\n\t\t\t\tthis.$emit('focus',e)\r\n\t\t\t},\r\n\t\t\tclearVal(e){\r\n\t\t\t\tthis.valdata =\"\";\r\n\t\t\t\tthis.$emit('clear', this.valdata)\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.tm-input {\r\n\r\n\t\t.tm-input-center {\r\n\t\t\twidth: 100%;\r\n\t\t\t.tm-input-center-wk{\r\n\t\t\t\ttransition: all 0.2s;\r\n\t\t\t}\r\n\t\t\t.tm-input-center-input {\r\n\t\t\t\tborder: none;\r\n\t\t\t\tbackground: none;\r\n\t\t\t\tbox-shadow: 0;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.tm-input-left {\r\n\t\t\tflex-shrink: 0;\r\n\t\t\theight: 100%;\r\n\t\t\t\r\n\t\t\tmin-width: 80rpx;\r\n\t\t\tpadding-right: 24rpx;\r\n\t\t}\r\n\r\n\t\t.tm-input-col {\r\n\t\t\t.tm-input-left {\r\n\t\t\t\tmax-width: inherit;\r\n\t\t\t\tpadding-right: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.tm-input-center {\r\n\r\n\t\t\t\t.tm-input-center-input {\r\n\r\n\t\t\t\t\theight: 76upx;\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.tm-input-right {\r\n\t\t\theight: 100%;\r\n\t\t\t// width: 300upx;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-input.vue?vue&type=style&index=0&id=3b4f5966&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-input.vue?vue&type=style&index=0&id=3b4f5966&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775132\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}