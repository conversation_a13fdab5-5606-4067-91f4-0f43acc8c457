{"version": 3, "sources": ["webpack:///D:/source/bill-view/tm-vuetify/components/tm-upload/tm-upload.vue?8bed", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-upload/tm-upload.vue?1538", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-upload/tm-upload.vue?6841", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-upload/tm-upload.vue?1155", "uni-app:///tm-vuetify/components/tm-upload/tm-upload.vue", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-upload/tm-upload.vue?7481", "webpack:///D:/source/bill-view/tm-vuetify/components/tm-upload/tm-upload.vue?87ce"], "names": ["components", "tmIcons", "name", "props", "showSort", "type", "default", "model", "black", "grid", "imgHeight", "max", "maxsize", "color", "delDirection", "disabled", "url", "filelist", "<PERSON><PERSON><PERSON><PERSON>", "header", "fileName", "tips", "autoUpload", "round", "fllowTheme", "code", "width", "responseStu", "data", "msg", "computed", "header_obj", "black_tmeme", "color_tmeme", "max<PERSON><PERSON><PERSON>", "itemWidth", "itemHeight", "list", "upObje", "created", "mounted", "t", "plist", "status", "progress", "fileId", "statusCode", "updated", "methods", "prevSort", "nowfilelist", "getRect", "errorFile", "id", "pushFile", "addfile", "maxfile", "opts", "uploadUrl", "isAuto", "clist", "stopupload", "startupload", "del", "changeSuccess", "getFile", "clearAllFile"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCsFjrB;EACAA;IAAAC;EAAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IAEA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;IACA;IACAC;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;QACA;MACA;IACA;IAEA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;QACA;MACA;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAJ;MACAG;MACAC;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;QACA;UACAmB;UAAA;UACAG;UAAA;UACAC;QACA;MACA;IACA;EACA;;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EACAL;IACA;MACAM;MACAC;MACAC;MACAC;MAEAC;IACA;EACA;EACAC;IAEA;EAEA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cACA;gBACAC;gBACAA;kBACA;kBACA;oBACA1B;kBACA;oBACAA;kBACA;kBACAyB;oBACAzB;oBACA2B;oBACAC;oBACAC;oBACAC;oBACAlB;kBACA;gBACA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAGA;EACAmB;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAV;QACA;QACAA;QACAA;QACA;UACAA;QACA;MACA;IACA;IACAW;MACA;MACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACAZ;QACA;QACA;UACA1B;QACA;UACAA;QACA;QACAyB;UACAzB;UACA2B;UACAC;UACAC;UACAC;UACAlB;QACA;MACA;IACA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACAd;gBACAe;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAEA;gBACAxC;gBACA;kBACA;oBACAyC;sBAAAtC;sBAAAjB;oBAAA;oBACAsD;oBACAE;oBACAC;oBACA/C;oBACAa;oBACAE;kBACA;kBACA;kBACA;kBACA;oBACAc;kBACA;gBACA;kBACA;oBAAA7B;oBAAA4C;oBAAA/B;oBAAAE;oBAAA8B;sBAAAtC;sBAAAjB;oBAAA;kBAAA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAA0D;gBACA;kBACAnB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAoB;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QAEA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UACA/C;QACA;MACA;MACA;MACA;IACA;IACA;IACAgD;MACA;MACA;QACA;UACAhD;QACA;MACA;MACA;IACA;IACA;IACAiD;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACtZA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tm-vuetify/components/tm-upload/tm-upload.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tm-upload.vue?vue&type=template&id=0af17bf9&scoped=true&\"\nvar renderjs\nimport script from \"./tm-upload.vue?vue&type=script&lang=js&\"\nexport * from \"./tm-upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tm-upload.vue?vue&type=style&index=0&id=0af17bf9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0af17bf9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tm-vuetify/components/tm-upload/tm-upload.vue\"\nexport default component.exports", "export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-upload.vue?vue&type=template&id=0af17bf9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  _vm.$initSSP()\n  var g0 = _vm.showSort ? _vm.list.length : null\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n      _vm.$setSSP(\"img\", {\n        info: {\n          itemWidth: _vm.itemWidth,\n          itemHeight: _vm.itemHeight,\n        },\n      })\n    }\n    return {\n      $orig: $orig,\n    }\n  })\n  var g1 = _vm.list.length < _vm.max && !_vm.disabled\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      $event.stopPropagation()\n      return _vm.$tm.preview(item.url, _vm.list, \"url\")\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-upload.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"tm-upload flex-start relative\" id=\"tm-upload\">\r\n\t\t<view v-for=\"(item,index) in list\" :key=\"index\" class=\"tm-upload-item  \" :class=\"[grid!=1?'ma-4':'']\" :style=\"{\r\n\t\t\twidth:itemWidth+'px',\r\n\t\t\theight:itemHeight+'px'\r\n\t\t}\">\r\n\t\t\t<view v-if=\"!disabled\" class=\"tm-upload-del \" :class=\"[delDirection]\">\r\n\t\t\t\t<slot name=\"del\">\r\n\t\t\t\t\t<tm-icons @click=\"del(index)\" :black=\"black_tmeme\" name=\"icon-times-circle-fill\" size=\"36\" color=\"red\"></tm-icons>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<view @click.stop=\"$tm.preview(item.url,list,'url')\" class=\"tm-upload-item-ck text flex-center  overflow\" \r\n\t\t\t:class=\"[color_tmeme,black_tmeme?'grey-darken-4 bk':'',`round-${round}`]\">\r\n\t\t\t\t<slot name=\"img\" :info={itemWidth,itemHeight}>\r\n\t\t\t\t\t<tm-icons style=\"line-height: 0;\" name=\"icon-exclamationcircle-f\" v-if=\"item['loaderror']==true\"></tm-icons>\n\t\t\t\t\t<image :mode=\"model\" v-else :src=\"item.url\" @error=\"errorFile(item,index)\" :style=\"{\n\t\t\t\t\t\twidth:itemWidth+'px',\n\t\t\t\t\t\theight:itemHeight+'px'\n\t\t\t\t\t}\"></image>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<!-- 上传提示语。 -->\r\n\t\t\t<view v-if=\"tips&&!disabled\" class=\"tm-upload-tips text-size-xs round-b-2\"\r\n\t\t\t :class=\"[\r\n\t\t\t\t item.statusCode==2||item.statusCode==4?'red text':'',\r\n\t\t\t\t item.statusCode==1||item.statusCode==0?'black text':'',\r\n\t\t\t\t item.statusCode==3?color_tmeme+' text':'',\r\n\t\t\t\t black_tmeme?'bk':''\r\n\t\t\t ]\"\r\n\t\t\t >{{item.status}}</view>\r\n\t\t\t<!-- 上传的进度。 -->\r\n\t\t\t<view v-if=\"item.progress>0&&item.progress!=100&&!disabled\" class=\"tm-upload-pro green\"\r\n\t\t\t\t:style=\"{width:item.progress+'%'}\"></view>\r\n\t\t\t<!-- 上传的排序。 -->\r\n\t\t\t<view v-if=\"showSort\" class=\"absolute  l-0 fulled flex-between\" :class=\"[disabled?'b-0':'b-40']\" :style=\"{height:'46rpx'}\">\r\n\t\t\t\t<view @click.stop=\"prevSort(item,index,'prev')\" class=\"round-r-24 flex-center px-16 py-6\" :class=\"[index==0?'opacity-0':'']\" style=\"background-color: rgba(0, 0, 0, 0.3);\">\r\n\t\t\t\t\t<tm-icons name=\"icon-angle-left\" size=\"24\" :color=\"color_tmeme\"></tm-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @click.stop=\"prevSort(item,index,'next')\" class=\"round-l-24 flex-center px-16 py-6\" :class=\"[index==list.length-1?'opacity-0':'']\" style=\"background-color: rgba(0, 0, 0, 0.3);\">\r\n\t\t\t\t\t<tm-icons name=\"icon-angle-right\" size=\"24\" :color=\"color_tmeme\"></tm-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view  @click=\"addfile\" v-if=\"list.length<max&&!disabled\" class=\"tm-upload-item  ma-4 grey-lighten-4 \" :class=\"[`round-${round}`]\" :style=\"{\r\n\t\t\twidth:itemWidth+'px',\r\n\t\t\theight:itemHeight+'px'\r\n\t\t}\">\r\n\t\t\t<view class=\"tm-upload-item-ck border-a-0 flex-center  text \" :class=\"[color_tmeme,black_tmeme?'grey-darken-4 bk':'',`round-${round}`]\">\r\n\t\t\t\t<slot name=\"upload\">\r\n\t\t\t\t\t<tm-icons name=\"icon-plus\" size=\"36\" :color=\"color_tmeme\"></tm-icons>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * 上传图片组件\r\n\t * @property {Function} change 每一张图片上传成功都传动触发，并返回上传成功的图片列表。\r\n\t * @property {Function} del 删除一张图片时触发，返回当前删除的图片数据。\r\n\t * @property {Number|String} grid = [1|2|3|4|5] 默认：5，一行排几个。\r\n\t * @property {Number} code = [] 默认：0，服务器上传返回数据中表示成功的标志码。\r\n\t * @property {Number} width = [] 默认：0，自定义组件宽度。如果0，自动获取。\r\n\t * @property {Number|String} img-height = [0] 默认：0，宽高相等。单位upx,自定义图片高度。\r\n\t * @property {Number|String} max = [9] 默认：9，最大上传数量\r\n\t * @property {String} del-direction = [left|right|center] 默认：right， 删除按钮的方向。left,right,center\r\n\t * @property {String|Boolean} disabled = [true|false] 默认：false， 如果禁用，会隐藏上传和删除按钮,只显示已上传的图片。\r\n\t * @property {String} url = [] 默认：\"\"，上传的地址。\r\n\t * @property {Array} filelist = [] 默认：[]，默认上传显示的图片。如果加上filelist.sync的话，会自动更新数据实现双向绑定。类似于v-model;\r\n\t * @property {String} url-key = [] 默认：\"\"，返回数据时，如果返回的是对象。则需要提供对象图像地址的key。默认没有，返回的即是图片地址。\r\n\t * @property {Object} header = [] 默认：{}，上传的头部参数。\r\n\t * @property {String} file-name = [file] 默认：file，上传时的文件key名。\r\n\t * @property {String} model = [scaleToFill|aspectFit|aspectFill|widthFix|heightFix|top|bottom|center|left|right|top left|top right|bottom left|bottom right] 默认：scaleToFill,图片展现模式，同官方。\r\n\t * @property {String} name = [] 默认：''，提交表单时的的字段名称标识\r\n\t * @property {Boolean|String} tips = [true|false] 默认：true，是否显示底部的上传提示语。上传中，失败等。\r\n\t * @property {Boolean|String} black = [true|false] 默认：null，暗黑模式。\r\n\t * @property {Boolean|String} auto-upload = [true|false] 默认：false，是否自动上传，即添加完图片后立即上传。\r\n\t * @property {Number|String} round = [] 默认：3，圆角\r\n\t * @property {Object} responseStu = [] 默认： {code:'code',//服务器返回的码的字段名称data:'data',//服务上传成功后返回 的数据字段名称msg:'msg'//服务器响应信息的字段名称。}，服务器响应结构字段映射表\r\n\t * @property {Number|String} maxsize = [] 默认：10*1024*1024，最大上传的图片大小，10mb大小\r\n\t * @example <tm-upload></tm-upload>\r\n\t * @description 可以通过refs.组件获得：addfile主动触发添加文件，stopupload停止上传，startupload开始或者继续上传，del删除一张图片。\r\n\t */\r\n\timport tmIcons from \"@/tm-vuetify/components/tm-icons/tm-icons.vue\"\r\n\texport default {\r\n\t\tcomponents:{tmIcons},\r\n\t\tname: \"tm-upload\",\r\n\t\tprops: {\r\n\t\t\tshowSort:{\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:false\r\n\t\t\t},\r\n\t\t\tmodel:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'scaleToFill'\r\n\t\t\t},\r\n\t\t\tblack:{\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:null\r\n\t\t\t},\r\n\t\t\t// 一行几个。\r\n\t\t\tgrid: {\r\n\t\t\t\ttype: String | Number,\r\n\t\t\t\tdefault: 5\r\n\t\t\t},\r\n\t\t\t// 默认0即为宽高相等。单位upx\r\n\t\t\timgHeight: {\r\n\t\t\t\ttype: String | Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 最大上传数量，默认9\r\n\t\t\tmax: {\r\n\t\t\t\ttype: String | Number,\r\n\t\t\t\tdefault: 9\r\n\t\t\t},\r\n\t\t\t// 最大上传数量，默认9\r\n\t\t\tmaxsize: {\r\n\t\t\t\ttype: String | Number,\r\n\t\t\t\tdefault: 10*1024*1024\r\n\t\t\t},\r\n\t\t\t// 主题色\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'primary'\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除按钮的方向。left,right,center\r\n\t\t\tdelDirection: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'right'\r\n\t\t\t},\r\n\t\t\t// 如果禁用，会隐藏上传和删除按钮。\r\n\t\t\tdisabled: String | Boolean,\r\n\t\t\t// 上传的地址。\r\n\t\t\turl: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 默认上传显示的图片。如果加上filelist.sync的话，会自动更新数据实现双向绑定。类似于v-model;\r\n\t\t\tfilelist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t//返回数据时，如果返回的是对象。则图像地址的key名。默认没有，返回的即是图片地址。\r\n\t\t\turlKey:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:''\r\n\t\t\t},\r\n\t\t\t// 上传的头部参数。\r\n\t\t\theader:{\r\n\t\t\t\ttype:Object,\r\n\t\t\t\tdefault:()=>{\r\n\t\t\t\t\treturn {};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 上传时的文件key名。默认file\r\n\t\t\tfileName:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'file'\r\n\t\t\t},\r\n\t\t\t// 是否显示底部的上传提示语。上传中，失败等。\r\n\t\t\ttips: {\r\n\t\t\t\ttype: Boolean|String,\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t\t// 是否自动上传，即添加完图片后立即上传。\r\n\t\t\tautoUpload: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\t//提交表单时的的字段名称\r\n\t\t\tname:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:''\r\n\t\t\t},\r\n\t\t\tround:{\r\n\t\t\t\ttype:Number|String,\r\n\t\t\t\tdefault:3\r\n\t\t\t},\r\n\t\t\t// 跟随主题色的改变而改变。\r\n\t\t\tfllowTheme:{\r\n\t\t\t\ttype:Boolean|String,\r\n\t\t\t\tdefault:true\r\n\t\t\t},\r\n\t\t\t//定义上传成功返回的code码，默认是0表示上传成功 。\r\n\t\t\tcode:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:0\r\n\t\t\t},\r\n\t\t\twidth:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:0\r\n\t\t\t},\r\n\t\t\t//上成功后，服务器顺应数据的字段映射表。\r\n\t\t\tresponseStu:{\r\n\t\t\t\ttype:Object,\r\n\t\t\t\tdefault:()=>{\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tcode:'code',//服务器返回的码的字段名称\r\n\t\t\t\t\t\tdata:'data',//服务上传成功后返回 的数据字段名称\r\n\t\t\t\t\t\tmsg:'msg'//服务器响应信息的字段名称。\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\theader_obj:function () {\r\n\t\t\t\treturn this.header;\r\n\t\t\t},\r\n\t\t\tblack_tmeme: function() {\r\n\t\t\t\tif (this.black !== null) return this.black;\r\n\t\t\t\treturn this.$tm.vx.state().tmVuetify.black;\r\n\t\t\t},\r\n\t\t\tcolor_tmeme:function(){\r\n\t\t\t\tif(this.$tm.vx.state().tmVuetify.color!==null&&this.$tm.vx.state().tmVuetify.color && this.fllowTheme){\r\n\t\t\t\t\treturn this.$tm.vx.state().tmVuetify.color;\r\n\t\t\t\t}\r\n\t\t\t\treturn this.color;\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmaxWidth: 0,\r\n\t\t\t\titemWidth: 0,\r\n\t\t\t\titemHeight: 0,\r\n\t\t\t\tlist: [],\r\n\t\t\t\t\r\n\t\t\t\tupObje:null,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// #ifdef APP-VUE || APP-PLUS  || MP\r\n\t\t\tthis.showSheet = false;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\tlet t = this;\r\n\t\t\tif (typeof t.filelist === 'object' && Array.isArray(t.filelist)) {\r\n\t\t\t\tlet plist = [...t.filelist];\r\n\t\t\t\tplist.forEach((item, index) => {\r\n\t\t\t\t\tlet url = \"\";\r\n\t\t\t\t\tif (typeof item === 'string') {\r\n\t\t\t\t\t\turl = item;\r\n\t\t\t\t\t} else if (typeof item === 'object') {\r\n\t\t\t\t\t\turl = item[t.urlKey]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tt.list.push({\r\n\t\t\t\t\t\turl: url,\r\n\t\t\t\t\t\tstatus: \"上传成功\",\r\n\t\t\t\t\t\tprogress: 100,\r\n\t\t\t\t\t\tfileId: t.$tm.guid(),\r\n\t\t\t\t\t\tstatusCode: 3,\r\n\t\t\t\t\t\tdata: item,\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\r\n\t\t\t}\r\n\t\t\tthis.getRect()\r\n\t\t\t\t\r\n\r\n\t\t},\r\n\t\tupdated() {\r\n\t\t\tthis.getRect()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tprevSort(item,index,type){\r\n\t\t\t\tif((index==0&&type=='prev')||(index==this.list.length-1&&type=='next')){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet nowindex = type=='prev'?index-1:index+1\r\n\t\t\t\tlet nowItem = this.list[index];\r\n\t\t\t\tlet newnowItem = this.list[nowindex];\r\n\t\t\t\tlet nowfilelist= [...this.list]\r\n\t\t\t\tnowfilelist.splice(index,1,newnowItem)\r\n\t\t\t\tnowfilelist.splice(nowindex,1,nowItem)\r\n\t\t\t\tthis.list = [...nowfilelist]\r\n\t\t\t\tthis.$emit('update:filelist', nowfilelist);\r\n\t\t\t},\r\n\t\t\tgetRect(){\r\n\t\t\t\tlet t = this;\r\n\t\t\t\tthis.$Querey('.tm-upload', this,0).then(o=>{\r\n\t\t\t\t\t\tif(!o[0].width&&t.maxWidth) return;\r\n\t\t\t\t\t\tt.maxWidth = o[0].width||t.width;\r\n\t\t\t\t\t\tlet itemWidth = (t.maxWidth - (parseInt(t.grid) - 1) * uni.upx2px(12)) / parseInt(t.grid);\r\n\t\t\t\t\t\tt.itemWidth = itemWidth;\r\n\t\t\t\t\t\tt.itemHeight = t.itemWidth;\r\n\t\t\t\t\t\tif (t.imgHeight > 0) {\r\n\t\t\t\t\t\t\tt.itemHeight = parseInt(uni.upx2px(t.imgHeight));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t},\n\t\t\terrorFile(item,index){\n\t\t\t\tlet id = item;\n\t\t\t\tid['loaderror'] = true;\n\t\t\t\tthis.list.splice(index,1,id)\n\t\t\t},\r\n\t\t\t//动态添加默认已上传的文件。\r\n\t\t\tpushFile(list){\r\n\t\t\t\tlet t= this;\r\n\t\t\t\tlet plist = list||[];\r\n\t\t\t\tplist.forEach((item, index) => {\r\n\t\t\t\t\tlet url = \"\";\r\n\t\t\t\t\tif (typeof item === 'string') {\r\n\t\t\t\t\t\turl = item;\r\n\t\t\t\t\t} else if (typeof item === 'object') {\r\n\t\t\t\t\t\turl = item[t.urlKey]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tt.list.push({\r\n\t\t\t\t\t\turl: url,\r\n\t\t\t\t\t\tstatus: \"上传成功\",\r\n\t\t\t\t\t\tprogress: 100,\r\n\t\t\t\t\t\tfileId: t.$tm.guid(),\r\n\t\t\t\t\t\tstatusCode: 3,\r\n\t\t\t\t\t\tdata: item,\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync addfile() {\r\n\t\t\t\tif(this.disabled) return;\r\n\t\t\t\tlet t = this;\r\n\t\t\t\tlet maxfile = parseInt(this.max) - this.list.length;\r\n\t\t\t\tif (maxfile <= 0) {\r\n\t\t\t\t\tthis.$tm.toast(\"已达上传上限\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t};\r\n\t\t\t\tlet url = this.url;\r\n\t\t\t\tif(!this.upObje){\r\n\t\t\t\t\tthis.upObje = new this.$tm.upload.uploadfile({\r\n\t\t\t\t\t\topts:{header:this.header_obj,name:this.fileName},\r\n\t\t\t\t\t\tmaxfile:maxfile,\r\n\t\t\t\t\t\tuploadUrl:url,\r\n\t\t\t\t\t\tisAuto:this.autoUpload,\r\n\t\t\t\t\t\tmaxsize:this.maxsize,\r\n\t\t\t\t\t\tcode:this.code,\r\n\t\t\t\t\t\tresponseStu:this.responseStu\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 添加已有的图片。\r\n\t\t\t\t\tthis.upObje.addfile(this.list);\r\n\t\t\t\t\tthis.upObje.success = function(item){\r\n\t\t\t\t\t\tt.changeSuccess();\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.upObje.setConfig({maxsize:this.maxsize,maxfile:maxfile,code:this.code,responseStu:this.responseStu,opts:{header:this.header_obj,name:this.fileName}});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tlet clist = await this.upObje.chooesefile().catch(e=>{});\r\n\t\t\t\tif(clist){\r\n\t\t\t\t\tt.list = clist;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 停止下载\r\n\t\t\tstopupload(){\r\n\t\t\t\tif(this.disabled) return;\r\n\t\t\t\tif(this.upObje){\r\n\t\t\t\t\tthis.upObje.stop();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 继续上传或者开始上传。\r\n\t\t\tstartupload(){\r\n\t\t\t\tif(this.disabled) return;\r\n\t\t\t\tif(this.upObje){\r\n\r\n\t\t\t\t\tthis.upObje.start();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 删除一张图片。\r\n\t\t\tdel(index) {\r\n\t\t\t\tif(this.disabled) return;\r\n\t\t\t\tthis.$emit(\"del\",this.list[index])\r\n\t\t\t\tthis.list.splice(index, 1);\r\n\t\t\t\tthis.changeSuccess();\r\n\t\t\t},\r\n\t\t\t// 只有上传成功才会触发change。并更新发送数据。\r\n\t\t\tchangeSuccess() {\r\n\t\t\t\tlet filelist = [];\r\n\t\t\t\tthis.list.forEach((item, index) => {\r\n\t\t\t\t\tif (item.statusCode === 3) {\r\n\t\t\t\t\t\tfilelist.push(item.data);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.$emit('change', filelist);\r\n\t\t\t\tthis.$emit('update:filelist', filelist);\r\n\t\t\t},\r\n\t\t\t//获取已经上传的图像。\r\n\t\t\tgetFile(){\r\n\t\t\t\tlet filelist = [];\r\n\t\t\t\tthis.list.forEach((item, index) => {\r\n\t\t\t\t\tif (item.statusCode === 3) {\r\n\t\t\t\t\t\tfilelist.push(item.data);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn filelist;\r\n\t\t\t},\r\n\t\t\t//清除所有已上传的文件。\r\n\t\t\tclearAllFile(){\r\n\t\t\t\tif(this.disabled) return;\r\n\t\t\t\tthis.$emit(\"clear\",[])\r\n\t\t\t\tthis.list=[];\r\n\t\t\t\tthis.changeSuccess();\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.tm-upload {\r\n\t\tflex-flow: wrap;\r\n\r\n\t\t.tm-upload-item {\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.tm-upload-tips {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 10;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\theight: 40upx;\r\n\t\t\t\tline-height: 40upx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 23upx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.tm-upload-pro {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 11;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\theight: 6upx;\r\n\t\t\t\twidth: 0%;\r\n\t\t\t}\r\n\r\n\t\t\t.tm-upload-del {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 10;\r\n\r\n\t\t\t\t&.right {\r\n\t\t\t\t\tright: -6upx;\r\n\t\t\t\t\ttop: -8upx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.left {\r\n\t\t\t\t\tleft: -6upx;\r\n\t\t\t\t\ttop: -8upx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.center {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.tm-upload-item-ck {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-upload.vue?vue&type=style&index=0&id=0af17bf9&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../develop/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tm-upload.vue?vue&type=style&index=0&id=0af17bf9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751286775138\n      var cssReload = require(\"D:/develop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}